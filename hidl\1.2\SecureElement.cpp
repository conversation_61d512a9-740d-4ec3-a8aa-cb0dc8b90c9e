/******************************************************************************
 *
 *  Copyright 2018 NXP
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 ******************************************************************************/

/******************************************************************************
 *
 *  The original Work has been changed by Tsingteng MicroSystem.
 *
 *  Copyright (C) 2021-2022 Tsingteng MicroSystem
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  NOT A CONTRIBUTION
 ******************************************************************************/
#include "SecureElement.h"

#ifdef TAG
#undef TAG
#endif
#define TAG    "SecureElement1.2"

namespace android {
namespace hardware {
namespace secure_element {
namespace V1_2 {
namespace implementation {

sp<V1_1::ISecureElementHalCallback> SecureElement::mCallbackV1_1 = nullptr;
sp<V1_0::ISecureElementHalCallback> SecureElement::mCallbackV1_0 = nullptr;

SecureElement::SecureElement() {
    tmsSeChannelsRecClean();
}

Return<void> SecureElement::init(
    const sp <
    ::android::hardware::secure_element::V1_0::ISecureElementHalCallback > &
    clientCallback) {
    int status;

    TMSLOG_D("%s: Enter", __func__);

    if (clientCallback == nullptr) {
        return Void();
    } else {
        mCallbackV1_0 = clientCallback;
        mCallbackV1_1 = nullptr;

        if (!mCallbackV1_0->linkToDeath(this, 0 /*cookie*/)) {
            TMSLOG_E("%s: Failed to register death notification", __func__);
        }
    }

    status = tmsSeInit();

    if (status != TMS_SUCCESS) {
        clientCallback->onStateChange(false);
    } else {
        clientCallback->onStateChange(true);
    }

    return Void();
}

Return<void> SecureElement::init_1_1(
    const sp <
    ::android::hardware::secure_element::V1_1::ISecureElementHalCallback > &
    clientCallback) {
    int status;
    TMSLOG_D("%s: Enter", __func__);

    if (clientCallback == nullptr) {
        return Void();
    } else {
        mCallbackV1_1 = clientCallback;
        mCallbackV1_0 = nullptr;

        if (!mCallbackV1_1->linkToDeath(this, 0 /*cookie*/)) {
            TMSLOG_E("%s: Failed to register death notification", __func__);
        }
    }

    status = tmsSeInit();

    if (status != TMS_SUCCESS) {
        clientCallback->onStateChange_1_1(false, "SE initialization failed");
    } else {
        clientCallback->onStateChange_1_1(true, "SE initialized");
    }

    return Void();
}

Return<void> SecureElement::getAtr(getAtr_cb _hidl_cb) {
    int status;
    hidl_vec<uint8_t> response;
    std::vector<uint8_t> atr;
    TMSLOG_D("%s: Enter", __func__);
    status = tmsSeGetAtr(atr);

    if (status == TMS_SUCCESS) {
        response.resize(atr.size());
        memcpy(response.data(), atr.data(), atr.size());
    }

    _hidl_cb(response);
    return Void();
}

Return<bool> SecureElement::isCardPresent() { return tmsIsSeCardPresent(); }

Return<void> SecureElement::transmit(const hidl_vec<uint8_t> &data,
                                     transmit_cb _hidl_cb) {
    int status = TMS_FAILED;
    hidl_vec<uint8_t> response;
    std::vector<uint8_t> cmdApdu;
    std::vector<uint8_t> rspApdu;

    TMSLOG_D("%s: Enter", __func__);
    cmdApdu.resize(data.size());
    memcpy(cmdApdu.data(), data.data(), data.size());
    // When OMAPI close basic channel, it will try to select NULL aid.
    // cmdApdu.len = 5 include CLA|INS|P1|P2|Le
    status = tmsSeTransmit(cmdApdu, rspApdu);

    if (status == TMS_SUCCESS) {
        response.resize(rspApdu.size());
        memcpy(response.data(), rspApdu.data(), rspApdu.size());
    }

    _hidl_cb(response);
    return Void();
}

Return<void> SecureElement::openLogicalChannel(const hidl_vec<uint8_t> &aid,
        uint8_t p2,
        openLogicalChannel_cb _hidl_cb) {
    int status = TMS_FAILED;
    LogicalChannelResponse resApduBuff;
    std::vector<uint8_t> tAid;
    std::vector<uint8_t> selectRsp;

    TMSLOG_D("%s: Enter", __func__);
    resApduBuff.channelNumber = 0xff;
    memset(&resApduBuff, 0x00, sizeof(resApduBuff));
    tAid.resize(aid.size());
    memcpy(tAid.data(), aid.data(), tAid.size());
    status = tmsOpenLogicalChannel(tAid, p2, &resApduBuff.channelNumber, selectRsp);
    bool doCloseChannel = true;
    SecureElementStatus sestatus;

    switch (status) {
        case TMS_SUCCESS:
            /*completely successful*/
            resApduBuff.selectResponse.resize(selectRsp.size());
            memcpy(resApduBuff.selectResponse.data(), selectRsp.data(), selectRsp.size());
            doCloseChannel = false;
            sestatus = SecureElementStatus::SUCCESS;
            break;

        case TMS_SE_STATUS_ITEM_NOT_FOUND:
            sestatus = SecureElementStatus::NO_SUCH_ELEMENT_ERROR;
            break;

        case TMS_STATUS_UNSUPPORTED:
            sestatus = SecureElementStatus::UNSUPPORTED_OPERATION;
            break;

        case TMS_STATUS_COMMUNICATION_ERROR:
            doCloseChannel = false;
            sestatus = SecureElementStatus::IOERROR;
            break;

        default:
            sestatus = SecureElementStatus::FAILED;
            break;
    }

    if (doCloseChannel) {
        TMSLOG_E("%s: Select APDU failed! Close channel..", __func__);
        if (closeChannel(resApduBuff.channelNumber) == SecureElementStatus::SUCCESS) {
            resApduBuff.channelNumber = 0xff;
        }
    }

    _hidl_cb(resApduBuff, sestatus);
    return Void();
}

Return<void> SecureElement::openBasicChannel(const hidl_vec<uint8_t> &aid,
        uint8_t p2,
        openBasicChannel_cb _hidl_cb) {
    hidl_vec<uint8_t> response;
    int status = TMS_FAILED;
    std::vector<uint8_t> tAid;
    std::vector<uint8_t> selectRsp;

    TMSLOG_D("%s: Enter", __func__);
    tAid.resize(aid.size());
    memcpy(tAid.data(), aid.data(), tAid.size());
    status = tmsOpenBasicChannel(tAid, p2, selectRsp);
    bool doCloseChannel = true;
    SecureElementStatus sestatus;

    switch (status) {
        case TMS_SUCCESS:
            /*completely successful*/
            response.resize(selectRsp.size());
            memcpy(response.data(), selectRsp.data(), selectRsp.size());
            doCloseChannel = false;
            sestatus = SecureElementStatus::SUCCESS;
            break;

        case TMS_SE_STATUS_ITEM_NOT_FOUND:
            sestatus = SecureElementStatus::NO_SUCH_ELEMENT_ERROR;
            break;

        case TMS_STATUS_UNSUPPORTED:
            doCloseChannel = false;
            sestatus = SecureElementStatus::UNSUPPORTED_OPERATION;
            break;

        case TMS_STATUS_COMMUNICATION_ERROR:
            doCloseChannel = false;
            sestatus = SecureElementStatus::IOERROR;
            break;

        default:
            sestatus = SecureElementStatus::FAILED;
            break;
    }

    if (doCloseChannel) {
        TMSLOG_E("%s: failed! Close channel...", __func__);
        closeChannel(DEFAULT_BASIC_CHANNEL);
    }

    _hidl_cb(response, sestatus);
    return Void();
}

Return<::android::hardware::secure_element::V1_0::SecureElementStatus>
SecureElement::closeChannel(
    uint8_t channelNumber) {
    int status = TMS_FAILED;

    TMSLOG_D("%s: Enter", __func__);
    status = tmsSeCloseChannel(channelNumber);

    TMSLOG_I("%s: Closing channel [%d] is %s ", __func__, channelNumber,
             (status == TMS_SUCCESS ? "successful" : "failed"));

    return status == TMS_SUCCESS
           ? SecureElementStatus::SUCCESS
           : SecureElementStatus::FAILED;
}

void SecureElement::serviceDied(uint64_t /*cookie*/,
                                const wp<IBase> & /*who*/) {
    TMSLOG_E("%s: SecureElement serviceDied!!!", __func__);
    tmsSeDeInit();

    if (mCallbackV1_1 != nullptr) {
        mCallbackV1_1->unlinkToDeath(this);
        mCallbackV1_1 = nullptr;
    }
}

Return<::android::hardware::secure_element::V1_0::SecureElementStatus>
SecureElement::reset() {
    int status;
    TMSLOG_D("%s: Enter", __func__);
    mCallbackV1_1->onStateChange_1_1(false, "reset the SE");
    status = tmsSeReset();

    if (status != TMS_SUCCESS) {
        TMSLOG_E("%s: SecureElement reset failed!!", __func__);
    } else {
        mCallbackV1_1->onStateChange_1_1(true, "SE initialized");
    }

    return status == TMS_SUCCESS
           ? SecureElementStatus::SUCCESS
           : SecureElementStatus::FAILED;
}

}  // namespace implementation
}  // namespace V1_2
}  // namespace secure_element
}  // namespace hardware
}  // namespace android
