/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "TestUtils.h"

/**
 * 测试主入口
 * 初始化Google Test框架和Mock框架
 */
int main(int argc, char **argv) {
    // 初始化Google Test
    ::testing::InitGoogleTest(&argc, argv);
    
    // 初始化Google Mock
    ::testing::InitGoogleMock(&argc, argv);
    
    // 设置测试环境
    TestUtils::setupTestEnvironment();
    
    // 运行所有测试
    int result = RUN_ALL_TESTS();
    
    // 清理测试环境
    TestUtils::cleanupTestEnvironment();
    
    return result;
}
