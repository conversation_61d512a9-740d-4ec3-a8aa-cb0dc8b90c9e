/*
 * Copyright (c) 2023 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "TmsSePhControl.h"

#ifdef TAG
#undef TAG
#endif
#define TAG    "SePhCtrl"

#ifdef MTK_TRUSTONIC_TEE
static uint32_t spiEnableCnt = 0;

static bool setSpiClk(const bool enable) {
    char devName[50] = {0x00};
    int deviceFd = -1;
    int ret = -1;
    int openCnt = 0;
    int controlCnt = 0;
    int arg = enable ? SPI_CLK_ON : SPI_CLK_OFF;
    TMSLOG_I("%s: arg = %d, spiEnableCnt = %d",  __func__, arg, spiEnableCnt);

    if (spiEnableCnt > 0) {
        enable ? spiEnableCnt++ : spiEnableCnt--;
    }

    if (spiEnableCnt != 0) {
        return true;
    }

    ConfigGetString(devName, 50,  // size 50
                    NAME_TMS_ESE_DEV_NODE, strlen(NAME_TMS_ESE_DEV_NODE),
                    "/dev/tms_ese", strlen("/dev/tms_ese"));
    int oFlag = O_RDWR | O_NOCTTY | O_NONBLOCK;

    while ((deviceFd < 0) && (openCnt < OPEN_RETRY_TIME)) {
        deviceFd = open(devName, oFlag);

        if (deviceFd < 0) {
            openCnt++;
            usleep(WAIT_TIME);
        }
    }

    if (deviceFd < 0) {
        TMSLOG_E("%s: errno: %d, open %s failed!! spiDeviceId: %d, openCnt = %d",  __func__,
                 errno, devName, deviceFd, openCnt);
        return false;
    }

    if (!enable) {
        tmsesesvc_generic(TMS_GENERIC_CMD_SPI_CLK_CHECK, SPI_CLK_OFF, nullptr);
    }

    while ((ret < 0) && (controlCnt < CONTROL_RETRY_TIME)) {
        ret = ioctl(deviceFd, ESE_SPI_CLK_CONTROL, arg);
        controlCnt = (ret < 0) ? (controlCnt + 1) : (controlCnt + 0);
    }

    close(deviceFd);

    if (ret >= 0) {
        if (enable) {
            spiEnableCnt++;
            tmsesesvc_generic(TMS_GENERIC_CMD_SPI_CLK_CHECK, SPI_CLK_ON, nullptr);
        } else {
            spiEnableCnt = 0;
        }
    }

    TMSLOG_I("%s: spiDeviceId: %d, openCnt: %d, controlCnt: %d", __func__,
             deviceFd, openCnt, controlCnt);
    return (ret < 0) ? false : true;
}

bool requestSpiClk() {
    return setSpiClk(true);
}

bool releaseSpiClk() {
    return setSpiClk(false);
}

bool releaseSpiClk(uint8_t num) {
    if (num < 1) {
        return false;
    }

    TMSLOG_D("%s: spiEnableCnt = %d, releasing num = %d", __func__,
             spiEnableCnt, num - 1);
    spiEnableCnt -= (num - 1);
    return setSpiClk(false);
}

bool hardReset() {
    char devName[50] = {0x00};
    int deviceFd = -1;
    int ret = -1;

    errno = 0;
    ConfigGetString(devName, 50,  // size 50
                    NAME_TMS_ESE_DEV_NODE, strlen(NAME_TMS_ESE_DEV_NODE),
                    "/dev/tms_ese", strlen("/dev/tms_ese"));
    int oFlag = O_RDWR | O_NOCTTY | O_NONBLOCK;
    deviceFd = open(devName, oFlag);

    if (deviceFd < 0) {
        TMSLOG_E(" open %s failed!!", devName);
        return false;
    }

    ret = ioctl(deviceFd, ESE_HARD_RESET, 0);
    close(deviceFd);
    TMSLOG_E("%s: errno = 0x%d", __func__, errno);
    return (ret < 0) ? false : true;
}
#endif
