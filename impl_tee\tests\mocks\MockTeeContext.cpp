/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#include "MockTeeContext.h"
#include "TestUtils.h"

// MockTeeContext静态成员定义
std::unique_ptr<MockTeeContext> MockTeeContext::s_instance = nullptr;

MockTeeContext* MockTeeContext::getInstance() {
    return s_instance.get();
}

void MockTeeContext::setInstance(std::unique_ptr<MockTeeContext> instance) {
    s_instance = std::move(instance);
}

// MockTeeSeApi静态成员定义
std::unique_ptr<MockTeeSeApi> MockTeeSeApi::s_instance = nullptr;

MockTeeSeApi* MockTeeSeApi::getInstance() {
    return s_instance.get();
}

void MockTeeSeApi::setInstance(std::unique_ptr<MockTeeSeApi> instance) {
    s_instance = std::move(instance);
}

// MockConfigManager静态成员定义
std::unique_ptr<MockConfigManager> MockConfigManager::s_instance = nullptr;

MockConfigManager* MockConfigManager::getInstance() {
    return s_instance.get();
}

void MockConfigManager::setInstance(std::unique_ptr<MockConfigManager> instance) {
    s_instance = std::move(instance);
}

// MockTestFixture实现
void MockTestFixture::SetUp() {
    // 创建Mock对象实例
    mockTeeContext_ = std::make_unique<::testing::NiceMock<MockTeeContext>>();
    mockTeeSeApi_ = std::make_unique<::testing::NiceMock<MockTeeSeApi>>();
    mockConfigManager_ = std::make_unique<::testing::NiceMock<MockConfigManager>>();
    
    // 设置全局Mock实例
    MockTeeContext::setInstance(std::move(mockTeeContext_));
    MockTeeSeApi::setInstance(std::move(mockTeeSeApi_));
    MockConfigManager::setInstance(std::move(mockConfigManager_));
    
    TestUtils::logTestInfo("Mock test fixture setup completed");
}

void MockTestFixture::TearDown() {
    // 清理Mock对象实例
    MockTeeContext::setInstance(nullptr);
    MockTeeSeApi::setInstance(nullptr);
    MockConfigManager::setInstance(nullptr);
    
    TestUtils::logTestInfo("Mock test fixture teardown completed");
}

#ifdef UNIT_TEST_BUILD
// 在单元测试构建时，重定向TEE API调用到Mock对象

extern "C" {

TEEC_Result TEEC_InitializeContext(const char* name, TEEC_Context* context) {
    if (MockTeeContext::getInstance()) {
        return MockTeeContext::getInstance()->InitializeContext(name, context);
    }
    return TEEC_ERROR_NOT_IMPLEMENTED;
}

void TEEC_FinalizeContext(TEEC_Context* context) {
    if (MockTeeContext::getInstance()) {
        MockTeeContext::getInstance()->FinalizeContext(context);
    }
}

TEEC_Result TEEC_OpenSession(TEEC_Context* context, TEEC_Session* session,
                            const TEEC_UUID* destination, uint32_t connectionMethod,
                            const void* connectionData, TEEC_Operation* operation,
                            uint32_t* returnOrigin) {
    if (MockTeeContext::getInstance()) {
        return MockTeeContext::getInstance()->OpenSession(context, session, destination,
                                                         connectionMethod, connectionData,
                                                         operation, returnOrigin);
    }
    return TEEC_ERROR_NOT_IMPLEMENTED;
}

void TEEC_CloseSession(TEEC_Session* session) {
    if (MockTeeContext::getInstance()) {
        MockTeeContext::getInstance()->CloseSession(session);
    }
}

TEEC_Result TEEC_InvokeCommand(TEEC_Session* session, uint32_t commandID,
                              TEEC_Operation* operation, uint32_t* returnOrigin) {
    if (MockTeeContext::getInstance()) {
        return MockTeeContext::getInstance()->InvokeCommand(session, commandID, operation, returnOrigin);
    }
    return TEEC_ERROR_NOT_IMPLEMENTED;
}

TEEC_Result TEEC_RegisterSharedMemory(TEEC_Context* context, TEEC_SharedMemory* sharedMem) {
    if (MockTeeContext::getInstance()) {
        return MockTeeContext::getInstance()->RegisterSharedMemory(context, sharedMem);
    }
    return TEEC_ERROR_NOT_IMPLEMENTED;
}

TEEC_Result TEEC_AllocateSharedMemory(TEEC_Context* context, TEEC_SharedMemory* sharedMem) {
    if (MockTeeContext::getInstance()) {
        return MockTeeContext::getInstance()->AllocateSharedMemory(context, sharedMem);
    }
    return TEEC_ERROR_NOT_IMPLEMENTED;
}

void TEEC_ReleaseSharedMemory(TEEC_SharedMemory* sharedMem) {
    if (MockTeeContext::getInstance()) {
        MockTeeContext::getInstance()->ReleaseSharedMemory(sharedMem);
    }
}

} // extern "C"

#endif // UNIT_TEST_BUILD
