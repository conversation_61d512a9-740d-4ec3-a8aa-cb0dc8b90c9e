/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#ifndef _TMS_SE_GPAPI_H_
#define _TMS_SE_GPAPI_H_

#include <stdio.h>
#include <string>
#include "TmsSePlatProp.h"
#include "TmsBase.h"
#include "TmsSeFeatures.h"
#if defined(MTK_TRUSTONIC_TEE) || defined(ENABLE_OPTEE_FUNC)
#include "tee_client_api.h"
#else
#include "TEE_client_api.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

#define APDULEN_R_STATUS    (2)
#define APDUMAXLEN_R_DATA   (256)
#define BUFFERSIZE          (65535)
//#define APDUMAXLEN_R_APDU   (APDUMAXLEN_R_DATA + APDULEN_R_STATUS)
#define APDUMAXLEN_R_APDU   BUFFERSIZE
#define CLA_CHANNEL_MASK    (0x03)
#define BASIC_CHANNLE_NUM   (0)
#define MAX_CHANNEL_NUM     (4)
#define MAX_READER_NUM      (4)

#define TMS_TEE_SE_SESSION_OPEN 0x00240000  // This equal to SE API TEE_SE_SESSION_OPEN

//SE Params mapping to TA
typedef struct {
    uint8_t       mode;
    uint8_t       pType; //Soc platform type.
    uint8_t       maxBlkRetryCnt; //t1SendSFrame, t1SendIFrame or t1SendRFrame returns error status.
    unsigned char debugLevel;  //log level of SE Hal.
    uint16_t      maxWTXCnt;     //max wtx request counter.
    uint16_t      maxIFSD;       //The interface device max IFS size.
    uint32_t      maxWriteRetryCnt; //ph write error retry counts.
    uint32_t      writeRetryTime;   //if ph write error, sleep writeRetryTime microsecond and retry.
    uint32_t      maxReadRetryCnt;  //ph read error retry counts.
    uint32_t      readRetryTime;    //if ph read error, sleep readRetryTime microsecond and retry.
    //T=1 protocol retry rules.
    //t1SendSFrame, t1SendIFrame or t1SendRFrame returns success status.
    //the max count of retry times when send frame error.
    uint32_t      maxRecoveryCnt;
    uint32_t      readTimeout;
    PlatProp      prop;
    uint8_t       features[MAX_FEATURES_SUPPORT];
} SEParams;

typedef enum {
    ESE_MODE_NORMAL = 0, //All wired transaction
    ESE_MODE_NFCC_DL,    //nfcc FW/BL download used
    ESE_MODE_ESE_DL,     //eSE COS download used
    ESE_MODE_ESE_PTH_DL, //eSE COS patch download used
} SeInitMode;

typedef enum {
    SELECT_FIRST    = 0x00,
    SELECT_LAST     = 0x01,
    SELECT_NEXT     = 0x02,
    SELECT_PREVIOUS = 0x03,
} SelectType;

typedef enum {
    //GP SE CMD
    GPTMS_CMD_SE_SERVICE_OPEN                      = 0x00000101,
    GPTMS_CMD_SE_SERVICE_CLOSE                     = 0x00000102,
    GPTMS_CMD_SE_SERVICE_GET_READERS               = 0x00000103,
    GPTMS_CMD_SE_READER_GET_PROPERTIES             = 0x00000201,
    GPTMS_CMD_SE_READER_GET_NAME                   = 0x00000202,
    GPTMS_CMD_SE_READER_OPEN_SESSION               = 0x00000203,
    GPTMS_CMD_SE_READER_CLOSE_SESSIONS             = 0x00000204,
    GPTMS_CMD_SE_SESSION_GET_ATR                   = 0x00000301,
    GPTMS_CMD_SE_SESSION_IS_CLOSED                 = 0x00000302,
    GPTMS_CMD_SE_SESSION_CLOSE                     = 0x00000303,
    GPTMS_CMD_SE_SESSION_CLOSE_CHANNELS            = 0x00000304,
    GPTMS_CMD_SE_SESSION_OPEN_BASIC_CHANNEL        = 0x00000305,
    GPTMS_CMD_SE_SESSION_OPEN_LOGICAL_CHANNEL      = 0x00000306,
    GPTMS_CMD_SE_CHANNEL_CLOSE                     = 0x00000401,
    GPTMS_CMD_SE_CHANNEL_SELECT_NEXT               = 0x00000402,
    GPTMS_CMD_SE_CHANNEL_GET_SELECT_RESPONSE       = 0x00000403,
    GPTMS_CMD_SE_CHANNEL_TRANSMIT                  = 0x00000404,
    GPTMS_CMD_SE_CHANNEL_GET_RESPONSE_LENGTH       = 0x00000405,
    GPTMS_CMD_SE_SECURE_CHANNEL_OPEN               = 0x00000601,
    GPTMS_CMD_SE_SECURE_CHANNEL_GET_SECURITY_LEVEL = 0x00000602,
    GPTMS_CMD_SE_SECURE_CHANNEL_CLOSE              = 0x00000603,

    //TMS CMDs
    TMS_CMD_SE_GENERIC                = 0x7F000000,
    TMS_CMD_SE_OPEN_BASIC_CHANNEL     = 0x7F000001,
    TMS_CMD_SE_OPEN_LOGICAL_CHANNEL   = 0x7F000002,
    TMS_CMD_SE_CHANNEL_GET_NUMBER_EXT = 0x7F000003,
    TMS_CMD_SE_CHANNEL_TRANSMIT_EXT   = 0x7F000004,
    TMS_CMD_SE_API_TEST               = 0x7F100000,

    TMS_CMD_MAX = 0x7FFFFFFF,
} TMS_CMD_ID;

typedef enum {
    TMS_GENERIC_CMD_RESET           = 0x00000000,
    TMS_GENERIC_CMD_HARD_RESET      = 0x00000001,
    TMS_GENERIC_CMD_SE_STATUS_CHECK = 0x00000002,
    TMS_GENERIC_CMD_INIT_CTX        = 0x00000003,
    TMS_GENERIC_CMD_TIMER           = 0x00000004,
    TMS_GENERIC_CMD_GET_VERSION     = 0x00000005,
    TMS_GENERIC_CMD_SPI_CLK_CHECK   = 0x00000006,
    TMS_GENERIC_CMD_SE_MODE         = 0x00000007,
} TMS_GENERIC_CMD_ID;

typedef enum {
    STATUS_CHECK = 0,
    HARD_RESET_CHECK,

    BASE_CHECK, /* Only chcek se init and deinit */
} SeCheckMode;

typedef struct __TEE_SEReaderProperties {
    bool sePresent;
    bool teeOnly;
    bool selectResponseEnable;
} TEE_SEReaderProperties;

typedef struct __TEE_SEAID {
    uint8_t *buffer;
    uint32_t bufferLen;
} TEE_SEAID;

typedef struct __TEE_SEServiceHandle *TEE_SEServiceHandle;
typedef struct __TEE_SEReaderHandle *TEE_SEReaderHandle;
typedef struct __TEE_SESessionHandle *TEE_SESessionHandle;
typedef struct __TEE_SEChannelHandle *TEE_SEChannelHandle;

/* UUID for tmsesesvc app */
const TEEC_UUID gpTmsSvcUUID = {
    0Xf2ac60d6, 0Xc56f, 0X4c25, {0X87, 0x24, 0X1c, 0Xd6, 0X6b, 0Xa0, 0X1d, 0X10}
};


size_t memscpy(void *dst, size_t dst_size, const void *src, size_t src_size);
/* Tms proprietary functions begin */
// for load tmsesesvc TA
TEEC_Result tmsesesvc_generic(uint32_t req, uint32_t val, TmsCapsule *inOut);
TEEC_Result tmsesesvc_open(SeInitMode initMode);
TEEC_Result tmsesesvc_close();
TEEC_Result TMSTEE_SESessionOpenBasicChannel(TEE_SESessionHandle
        seSessionHandle,
        TEE_SEAID *seAID, TEE_SEChannelHandle *seChannelHandle, uint8_t p2);
TEEC_Result TMSTEE_SESessionOpenLogicalChannel(TEE_SESessionHandle
        seSessionHandle,
        TEE_SEAID *seAID, TEE_SEChannelHandle *seChannelHandle, uint8_t p2,
        uint8_t *channelNum);
void initSvcSEParams(SeInitMode initMode);
/* Tms proprietary functions end */

/* GP TEE SE API Begin*/
TEEC_Result TEE_SEServiceOpen(TEE_SEServiceHandle *seServiceHandle);
void TEE_SEServiceClose(TEE_SEServiceHandle seServiceHandle);
TEEC_Result TEE_SEServiceGetReaders(TEE_SEServiceHandle seServiceHandle,
                                    TEE_SEReaderHandle *seReaderHandleList, uint32_t *seReaderHandleListLen);
void TEE_SEReaderGetProperties(TEE_SEReaderHandle seReaderHandle,
                               TEE_SEReaderProperties *readerProperties);
TEEC_Result TEE_SEReaderGetName(TEE_SEReaderHandle seReaderHandle,
                                char *readerName, uint32_t *readerNameLen);
TEEC_Result TEE_SEReaderOpenSession(TEE_SEReaderHandle seReaderHandle,
                                    TEE_SESessionHandle *seSessionHandle);
void TEE_SEReaderCloseSessions(TEE_SEReaderHandle seReaderHandle);
TEEC_Result TEE_SESessionGetATR(TEE_SESessionHandle seSessionHandle, void *atr,
                                uint32_t *atrLen);
TEEC_Result TEE_SESessionIsClosed(TEE_SESessionHandle seSessionHandle);
void TEE_SESessionClose(TEE_SESessionHandle seSessionHandle);
void TEE_SESessionCloseChannels(TEE_SESessionHandle seSessionHandle);
void TEE_SEChannelClose(TEE_SEChannelHandle seChannelHandle);
TEEC_Result TEE_SEChannelSelectNext(TEE_SEChannelHandle seChannelHandle);
TEEC_Result TEE_SEChannelGetSelectResponse(TEE_SEChannelHandle seChannelHandle,
        void *response, uint32_t *responseLen);
TEEC_Result TEE_SEChannelGetResponseLength(TEE_SEChannelHandle seChannelHandle,
        uint32_t *responseLen);
TEEC_Result TEE_SEChannelTransmit(TEE_SEChannelHandle seChannelHandle,
                                  void *command, uint32_t cmdLen, void *response, uint32_t *respLen);
/* GP TEE SE API End*/
#ifdef __cplusplus
}
#endif

#endif
