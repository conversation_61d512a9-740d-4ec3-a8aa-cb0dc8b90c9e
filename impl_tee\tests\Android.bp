/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

cc_test {
    name: "tms_seimpl_tee_unit_tests",
    vendor: true,
    owner: "tms",

    srcs: [
        "test_main.cpp",
        "adaptation/TmsSeAdaptationTest.cpp",
        "gpapi/TmsSeGpApiTest.cpp",
        "config/TmsSeGetConfigTest.cpp",
        "platprop/TmsSePlatPropTest.cpp",
        "mocks/MockTeeContext.cpp",
        "utils/TestUtils.cpp",
    ],

    local_include_dirs: [
        "include",
        "mocks",
        "utils",
    ],

    header_libs: [
        "securemsm_GPTEE_inc",
    ],

    include_dirs: [
        "vendor/qcom/proprietary/securemsm/GPTEE/inc",
    ],

    shared_libs: [
        "libc",
        "libcutils",
        "liblog",
        "libdl",
        "libutils",
        "libhardware",
        "libhidlbase",
        "libbase",
        "libGPTEE_vendor",
        "libbinder_ndk",
        "android.hardware.secure_element-V1-ndk",
    ],

    static_libs: [
        "libtms_base",
        "libtms_cosdl_aidl",
        "libtms_cosdl_trad",
        "libgtest",
        "libgmock",
    ],

    // 链接被测试的库
    whole_static_libs: [
        "libtms_seimpl_tee_for_test",
    ],

    cflags: [
        "-Wno-error=date-time",
        "-Wno-date-time",
        "-DUNIT_TEST_BUILD",
        "-DGTEST_HAS_STD_WSTRING=0",
        "-DGTEST_HAS_GLOBAL_WSTRING=0",
    ],

    test_suites: ["device-tests"],
}

// 为测试创建的静态库版本，便于mock和测试
cc_library_static {
    name: "libtms_seimpl_tee_for_test",
    vendor: true,
    owner: "tms",

    srcs: [
        "../src/TmsSeAdaptation.cpp",
        "../src/TmsSeGpApi.cpp",
        "../src/TmsSePlatProp.cpp",
        "../src/TmsSeGetConfig.cpp",
        "../extns/src/TmsSeFeatures.cpp",
        "../extns/src/TmsSeUpdate.cpp",
    ],

    local_include_dirs: [
        "../inc",
        "../extns/inc",
        "include",
        "mocks",
    ],

    header_libs: [
        "securemsm_GPTEE_inc",
    ],

    shared_libs: [
        "libc",
        "libcutils",
        "liblog",
        "libdl",
        "libutils",
        "libhardware",
        "libhidlbase",
        "libbase",
        "libGPTEE_vendor",
        "libbinder_ndk",
        "android.hardware.secure_element-V1-ndk",
    ],

    static_libs: [
        "libtms_base",
        "libtms_cosdl_aidl",
        "libtms_cosdl_trad",
    ],

    cflags: [
        "-Wno-error=date-time",
        "-Wno-date-time",
        "-DUNIT_TEST_BUILD",
    ],
}
