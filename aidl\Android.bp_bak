// Copyright (C) 2021-2022 Tsingteng MicroSystem
//
// All rights are reserved. Reproduction in whole or in part is
// prohibited without the written consent of the copyright owner.
//
// Tsingteng reserves the right to make changes without notice at any time.
//
// Tsingteng makes no warranty, expressed, implied or statutory, including but
// not limited to any implied warranty of merchantability or fitness for any
// particular purpose, or that the use will not infringe any third party patent,
// copyright or trademark. Tsingteng must not be liable for any loss or damage
// arising from its use.

cc_binary {
    name: "android.hardware.secure_element-service-tms",

    relative_install_path: "hw",
    vendor: true,

    init_rc: ["secure_element-service-tms.rc"],

    vintf_fragments: ["secure_element-service-tms.xml"],

    header_libs: [
        "securemsm_GPTEE_inc",
    ],

    //include_dirs: [
    //    "vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/ClientLib/include/GP",
    //],

    srcs: [
        "SEService.cpp",
        "SecureElement.cpp",
        "extns/*.cpp",
    ],

    shared_libs: [
        "android.hardware.secure_element-V1-ndk",
        "libbinder_ndk",
        "libbase",
        "libcutils",
        "libhardware",
        "libhidlbase",
        "liblog",
        "libutils",
        "libtms_seimpl_tee",
        "vendor.tms.tmsese_aidl-V1-ndk",
    ],

    static_libs: [
        "libtms_base",
        "libtms_cosdl_aidl",
        "libtms_cosdl_trad",
    ],

    local_include_dirs: [
        "extns",
        "../impl_tee/inc",
        "../impl_tee/extns/inc",
    ],

    cflags: [
        //"-DCHECK_UNION_PARAM",
    ],

    include_dirs: [
        //see condition_android_bp.go
    ],
}
