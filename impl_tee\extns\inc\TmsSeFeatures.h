/*
 * Copyright (c) 2024 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#ifndef _TMS_FEATURES_H_
#define _TMS_FEATURES_H_
#include <stdint.h>

#define MAX_FEATURES_SUPPORT 0x0A

#define MAX_BUFFER (65535)
#define LEN_TTR_HEAD 3  // TAG(1) + LEN(2)

// Fixed feature value len
#define LEN_EXACT_TEE_RESULT 4
#ifdef ENABLE_OPTEE_FUNC
#define LEN_LOG_BUFFER (2*1024)
#else
#define LEN_LOG_BUFFER (10 * 1024)
#endif

typedef enum {
    TEE_TRANSPORT_RULE = 0x00,
    EXACT_TEE_RESULT = 0x01,
    TEE_LOG_TRANSPORT = 0x02,
    ENCRYPTED_TEE_LOG_TRANSPORT =0x03,
    MAX_FEATURE_NUM = MAX_FEATURES_SUPPORT,
} TMS_TEE_FEATURE;

typedef enum {
    TAG_INVAID_EMPTY = 0x00,
    TAG_ORIGIN_DATA = 0x01,
    TAG_EXACT_TEE_RESULT = 0x02,
    TAG_TEE_LOG_TRANSPORT = 0x03,
    TAG_ENCRYPTED_TEE_LOG_TRANSPORT = 0x04,
} TTRTag;

/**
 * @brief Clear the feature list.
 *
 * This function clears the list of features stored in memory.
 * After calling this function, the list will be empty.
 */
void clearFeatureList();

/**
 * @brief check features form TA and calculate the commom feature.
 *
 * This function compare feature between CA and TA and set the common feature .
 * After calling this function, the feautre list will be set.
 */
void compareAndSetFeatures();

/**
 * @brief get features which is supported by CA.
 *
 * @note The returned pointer points to a constant array of uint8_t,
 *       where each byte represents a specific feature.
 *
 * @return A pointer to the feature list of the CA.
 */
const uint8_t* getCAFeatures();

/**
 * @brief Get the length of the CA features list.
 *
 * @return The length of the features list of the CA.
 */
uint32_t getCAFeaturesLen();

/**
 * @brief get common features which is supported by CA and TA.
 *
 * @note The returned pointer points to a constant array of uint8_t,
 *       where each byte represents a specific feature.
 *
 * @return A pointer to the common feature list.
 */
uint8_t* getFeatures();

/**
 * @brief Get the length of the common features list.
 *
 * @return The length of the common features list.
 */
uint32_t getFeaturesLen();

/**
 * @brief Check if the specified feature is supported.
 *
 * @param feature The feature to be checked.
 * @return true if the feature is supported, false otherwise.
 */
bool isSupportFeature(TMS_TEE_FEATURE feature);

/**
 * @brief Get the version of the specified feature.
 *
 * @param feature The feature to be checked.
 * @return The version of the specified feature.
 */
uint8_t getFeatureVersion(TMS_TEE_FEATURE feature);

/**
 * @brief Get needed memory size of all features' data and original data.
 *
 * This function will check all the feature in feature list. Then add up all
 * the memory needed by the feature's data and the original data.
 *
 * @param origin Length of original data.
 * @return Needed buffer size of all features' data and original data.
 */
uint32_t getFeaturesNeededMemSize(uint32_t origin);

/**
 * @brief Parse the buffer and retrieve the original data based on supported features.
 *
 * This function parses the source buffer using the supported features,
 * reconstructs the original data, and stores it into the destination buffer.
 *
 * @param dst Pointer to the destination buffer where the original data will be stored.
 * @param dst_size Size of the destination buffer in bytes.
 * @param src Pointer to the source buffer containing the data to be parsed.
 * @param src_size Size of the source buffer in bytes.
 *
 * @return The number of bytes of original data stored in the destination buffer.
 */
uint32_t parseBufferAndGetOriginData(void *dst, size_t dst_size, const uint8_t *src, size_t src_size);
#endif
