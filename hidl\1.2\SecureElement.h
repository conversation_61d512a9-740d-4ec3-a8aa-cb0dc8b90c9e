/******************************************************************************
 *
 *  Copyright 2018 NXP
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 ******************************************************************************/

/******************************************************************************
 *
 *  The original Work has been changed by Tsingteng MicroSystem.
 *
 *  Copyright (C) 2021-2022 Tsingteng MicroSystem
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  NOT A CONTRIBUTION
 ******************************************************************************/

#ifndef ANDROID_HARDWARE_SECURE_ELEMENT_V1_1_SECUREELEMENT_H
#define ANDROID_HARDWARE_SECURE_ELEMENT_V1_1_SECUREELEMENT_H

#include <android/hardware/secure_element/1.0/types.h>
#include <android/hardware/secure_element/1.2/ISecureElement.h>
#include <hidl/LegacySupport.h>
#include <hidl/MQDescriptor.h>
#include <hidl/Status.h>
#include <log/log.h>
#include <dlfcn.h>
#include <stdlib.h>
#include <string.h>
#ifdef CHECK_UNION_PARAM
#include <cutils/properties.h>
#endif
#include <vendor/tms/tmsese/1.0/ITmsEse.h>
#include "TmsEse.h"
#include "TmsSeAdaptation.h"

namespace android {
namespace hardware {
namespace secure_element {
namespace V1_2 {
namespace implementation {

using ::android::sp;
using ::android::hardware::hidl_vec;
using ::android::hardware::Return;
using ::android::hardware::Void;

using ::android::hardware::secure_element::V1_2::ISecureElement;

using ::android::hardware::secure_element::V1_0::LogicalChannelResponse;
using ::android::hardware::secure_element::V1_0::SecureElementStatus;
using ::android::hidl::base::V1_0::IBase;

struct SecureElement : public V1_2::ISecureElement,
    public hidl_death_recipient {
    SecureElement();
    Return<void> init(
        const sp<V1_0::ISecureElementHalCallback> &clientCallback) override;
    Return<void> init_1_1(
        const sp<V1_1::ISecureElementHalCallback> &clientCallback) override;
    Return<void> getAtr(getAtr_cb _hidl_cb) override;
    Return<bool> isCardPresent() override;
    Return<void> transmit(const hidl_vec<uint8_t> &data,
                          transmit_cb _hidl_cb) override;
    Return<void> openLogicalChannel(const hidl_vec<uint8_t> &aid, uint8_t p2,
                                    openLogicalChannel_cb _hidl_cb) override;
    Return<void> openBasicChannel(const hidl_vec<uint8_t> &aid, uint8_t p2,
                                  openBasicChannel_cb _hidl_cb) override;
    Return<::android::hardware::secure_element::V1_0::SecureElementStatus>
    closeChannel(
        uint8_t channelNumber) override;
    void serviceDied(uint64_t /*cookie*/, const wp<IBase> & /*who*/) override;
    Return<::android::hardware::secure_element::V1_0::SecureElementStatus> reset();

  private:
    static sp<V1_0::ISecureElementHalCallback> mCallbackV1_0;
    static sp<V1_1::ISecureElementHalCallback> mCallbackV1_1;
};

}  // namespace implementation
}  // namespace V1_2
}  // namespace secure_element
}  // namespace hardware
}  // namespace android

#endif  // ANDROID_HARDWARE_SECURE_ELEMENT_V1_2_SECUREELEMENT_H
