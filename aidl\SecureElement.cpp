/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "SecureElement.h"

#ifdef TAG
#undef TAG
#endif
#define TAG    "SecureElement"

namespace aidl {
namespace android {
namespace hardware {
namespace secure_element {

using ::aidl::android::hardware::secure_element::LogicalChannelResponse;

std::shared_ptr<ISecureElementCallback> SecureElement::mCallback = nullptr;
AIBinder_DeathRecipient *clientDeathRecipient = nullptr;

SecureElement::SecureElement() {
    tmsSeChannelsRecClean();
}

void OnDeath(void *cookie) {
    (void)cookie;
    TMSLOG_E("%s: SecureElement serviceDied!!!", __func__);
    SecureElement *se = static_cast<SecureElement *>(cookie);
    tmsSeDeInit();

    if (se->mCallback != nullptr) {
        se->mCallback = nullptr;
    }
}

ScopedAStatus SecureElement::init(
    const std::shared_ptr<ISecureElementCallback> &clientCallback) {
    int status;
    TMSLOG_D("%s: Enter", __func__);

    if (clientCallback == nullptr) {
        return ScopedAStatus::fromExceptionCode(EX_NULL_POINTER);
    } else {
        mCallback = clientCallback;
        clientDeathRecipient = AIBinder_DeathRecipient_new(OnDeath);
        auto ret =
            AIBinder_linkToDeath(clientCallback->asBinder().get(),
                                 clientDeathRecipient, this /* cookie */);

        if (ret != STATUS_OK) {
            TMSLOG_E("%s: linkToDeath failed:%d", __func__, ret);
            // Just ignore the error.
        }
    }

    status = tmsSeInit();

    if (status != TMS_SUCCESS) {
        mCallback->onStateChange(false, "SE initialization failed");
    } else {
        mCallback->onStateChange(true, "SE initialized");
    }

    return ScopedAStatus::ok();
}

ScopedAStatus SecureElement::getAtr(std::vector<uint8_t> *_aidl_return) {
    int status;
    std::vector<uint8_t> response;
    std::vector<uint8_t> atr;
    TMSLOG_D("%s: Enter", __func__);
    status = tmsSeGetAtr(atr);

    if (status == TMS_SUCCESS) {
        response.resize(atr.size());
        memcpy(response.data(), atr.data(), atr.size());
    }

    *_aidl_return = response;
    return ScopedAStatus::ok();
}

ScopedAStatus SecureElement::isCardPresent(bool *_aidl_return) {
    TMSLOG_D("%s: Enter", __func__);
    *_aidl_return = tmsIsSeCardPresent();
    return ScopedAStatus::ok();
}

ScopedAStatus SecureElement::transmit(const std::vector<uint8_t> &data,
                                      std::vector<uint8_t> *_aidl_return) {
    int status = TMS_FAILED;
    std::vector<uint8_t> rspApdu;
    std::vector<uint8_t> cmdApdu;
    TMSLOG_D("%s: Enter", __func__);
    cmdApdu.resize(data.size());
    memcpy(cmdApdu.data(), data.data(), data.size());
    // When OMAPI close basic channel, it will try to select NULL aid.
    // cmdApdu.len = 5 include CLA|INS|P1|P2|Le
    status = tmsSeTransmit(cmdApdu, rspApdu);
    *_aidl_return = rspApdu;
    return status == TMS_SUCCESS
           ? ScopedAStatus::ok()
           : ScopedAStatus::fromServiceSpecificError(FAILED);
}

ScopedAStatus SecureElement::openLogicalChannel(
    const std::vector<uint8_t> &aid, int8_t p2,
    ::aidl::android::hardware::secure_element::LogicalChannelResponse *
    _aidl_return) {
    int status = TMS_FAILED;
    LogicalChannelResponse resApduBuff;
    std::vector<uint8_t> tAid;
    std::vector<uint8_t> selectRsp;
    TMSLOG_D("%s: Enter", __func__);
    resApduBuff.channelNumber = 0xff;
    memset(&resApduBuff, 0x00, sizeof(resApduBuff));
    tAid.resize(aid.size());
    memcpy(tAid.data(), aid.data(), tAid.size());
    status = tmsOpenLogicalChannel(tAid, p2, (uint8_t *)&resApduBuff.channelNumber,
                                   selectRsp);
    bool doCloseChannel = true;

    switch (status) {
        case TMS_SUCCESS:
            /*completely successful*/
            resApduBuff.selectResponse.resize(selectRsp.size());
            memcpy(resApduBuff.selectResponse.data(), selectRsp.data(), selectRsp.size());
            doCloseChannel = false;
            break;

        case TMS_SE_STATUS_ITEM_NOT_FOUND:
            status = ISecureElement::NO_SUCH_ELEMENT_ERROR;
            break;

        case TMS_STATUS_UNSUPPORTED:
            status = ISecureElement::UNSUPPORTED_OPERATION;
            break;

        case TMS_STATUS_COMMUNICATION_ERROR:
            doCloseChannel = false;
            status = ISecureElement::IOERROR;
            break;

        default:
            status = ISecureElement::FAILED;
            break;
    }

    if (doCloseChannel) {
        TMSLOG_E("%s: Select APDU failed! Close channel...", __func__);
        if (closeChannel(resApduBuff.channelNumber).isOk()) {
            resApduBuff.channelNumber = 0xff;
        }
    }

    *_aidl_return = resApduBuff;
    return status == TMS_SUCCESS
           ? ndk::ScopedAStatus::ok()
           : ndk::ScopedAStatus::fromServiceSpecificError(status);
}

ScopedAStatus SecureElement::openBasicChannel(
    const std::vector<uint8_t> &aid, int8_t p2,
    std::vector<uint8_t> *_aidl_return) {
    std::vector<uint8_t> response;
    int status = TMS_FAILED;
    std::vector<uint8_t> tAid;
    std::vector<uint8_t> selectRsp;

    TMSLOG_D("%s: Enter", __func__);
    tAid.resize(aid.size());
    memcpy(tAid.data(), aid.data(), tAid.size());
    status = tmsOpenBasicChannel(tAid, p2, selectRsp);
    bool doCloseChannel = true;

    switch (status) {
        case TMS_SUCCESS:
            /*completely successful*/
            response.resize(selectRsp.size());
            memcpy(response.data(), selectRsp.data(), selectRsp.size());
            doCloseChannel = false;
            break;

        case TMS_SE_STATUS_ITEM_NOT_FOUND:
            status = ISecureElement::NO_SUCH_ELEMENT_ERROR;
            break;

        case TMS_STATUS_UNSUPPORTED:
            doCloseChannel = false;
            status = ISecureElement::UNSUPPORTED_OPERATION;
            break;

        case TMS_STATUS_COMMUNICATION_ERROR:
            doCloseChannel = false;
            status = ISecureElement::IOERROR;
            break;

        default:
            status = ISecureElement::FAILED;
            break;
    }

    if (doCloseChannel) {
        TMSLOG_E("%s: failed! Close channel...", __func__);
        closeChannel(DEFAULT_BASIC_CHANNEL);
    }

    *_aidl_return = response;
    return status == TMS_SUCCESS
           ? ScopedAStatus::ok()
           : ScopedAStatus::fromServiceSpecificError(status);
}

ScopedAStatus SecureElement::closeChannel(int8_t channelNumber) {
    int status = TMS_FAILED;

    TMSLOG_D("%s: Enter", __func__);
    status = tmsSeCloseChannel(channelNumber);

    if (status != TMS_SUCCESS) {
        status = ISecureElement::FAILED;
    }

    TMSLOG_I("%s: Closing channel [%d] is %s ", __func__, channelNumber,
             (status == TMS_SUCCESS ? "successful" : "failed"));
    return status == TMS_SUCCESS
           ? ScopedAStatus::ok()
           : ScopedAStatus::fromServiceSpecificError(status);
}

ScopedAStatus SecureElement::reset() {
    int status = TMS_SUCCESS;

    TMSLOG_D("%s: Enter", __func__);
    mCallback->onStateChange(false, "reset the SE");
    status = tmsSeReset();

    if (status != TMS_SUCCESS) {
        TMSLOG_E("%s: SecureElement reset failed!!", __func__);
        status = ISecureElement::FAILED;
    } else {
        mCallback->onStateChange(true, "SE initialized");
    }

    return status == TMS_SUCCESS
           ? ScopedAStatus::ok()
           : ScopedAStatus::fromServiceSpecificError(status);
}

}  // namespace secure_element
}  // namespace hardware
}  // namespace android
}  // namespace aidl
