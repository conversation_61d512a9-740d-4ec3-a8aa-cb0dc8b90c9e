/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "TmsEse.h"
#include "TmsSeAdaptation.h"
#include "TmsSeUpdate.h"
#ifdef MTK_TRUSTONIC_TEE
#include "TmsSePhControl.h"
#endif

#ifdef TAG
#undef TAG
#endif
#define TAG    "TmsEse"

namespace aidl {
namespace vendor {
namespace tms {
namespace tmsese_aidl {

using ::aidl::vendor::tms::tmsese_aidl::TmsSeGenericRsp;

::ndk::ScopedAStatus TmsEse::setMtkSpiClk(bool in_enable, bool *_aidl_return) {
    TMSLOG_D("%s: Enter, enable = %d", __func__, in_enable);
#ifdef MTK_TRUSTONIC_TEE
    static uint32_t externSpiCnt = 0;
    bool result = false;

    TMSLOG_I("%s: enable = %d, externSpiCnt = %d", __func__, in_enable, externSpiCnt);

    /* externSpiCnt prevent the client from releasing spiclk multiple times,
     * causing the spiclk of the CA to be released and a dump occurs */
    if (externSpiCnt == 0 && !in_enable) {
        TMSLOG_D("%s: stop releasing spi clk", __func__, in_enable, externSpiCnt);
        return true;
    }

    result = in_enable ? requestSpiClk() : releaseSpiClk();

    if (result) {
        in_enable ? externSpiCnt++ : externSpiCnt--;
    }

    *_aidl_return = result;
#else
    *_aidl_return = true;
#endif
    return ndk::ScopedAStatus::ok();
}

::ndk::ScopedAStatus TmsEse::generic(int32_t in_cmd,
                                     const std::vector<uint8_t> &in_data,
                                     ::aidl::vendor::tms::tmsese_aidl::TmsSeGenericRsp *_aidl_return) {
    TmsSeGenericRsp genericRsp;
    int status;
    UNUSED(in_data);

    TMSLOG_D("%s: Enter", __func__);
    memset(&genericRsp, 0x00, sizeof(genericRsp));

    switch (in_cmd) {
        case TMS_SE_GENERIC_HARD_RESET: {
                status = tmsSeHardReset();
                break;
            }

        case TMS_SE_GENERIC_UPDATE_COS: {
                status = (tmsSeCheckCosUpdate() == DLStatus::OK) ? TMS_SUCCESS : TMS_FAILED;
                break;
            }

        case TMS_SE_GENERIC_UPDATE_PATCH: {
                status = (tmsSeCheckCosPatchUpdate() == DLStatus::OK) ? TMS_SUCCESS : TMS_FAILED;
                break;
            }

        case TMS_SE_GENERIC_UPDATE_APPLET: {
                status = (tmsSeCheckAppletUpdate() == DLStatus::OK) ? TMS_SUCCESS : TMS_FAILED;
                break;
            }

        default: {
                TMSLOG_W("%s : invalid cmd", __func__);
                status = TMS_FAILED;
                break;
            }
    }

    genericRsp.status = status;
    *_aidl_return = genericRsp;
    TMSLOG_I("%s: cmd = %u, status = %d", __func__, in_cmd, status);
    return status == TMS_SUCCESS
           ? ndk::ScopedAStatus::ok()
           : ndk::ScopedAStatus::fromServiceSpecificError(status);
}

}  // namespace tmsese_aidl
}  // namespace tms
}  // namespace vendor
}  // namespace aidl
