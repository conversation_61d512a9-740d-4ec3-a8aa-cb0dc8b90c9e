package impl_tee

import (
    "android/soong/android"
    "android/soong/cc"
    "fmt"
)

type props struct {
    Cflags []string
    Shared_libs []string
    Srcs []string
}

func init() {
    fmt.Println("impl_tee init")
    android.RegisterModuleType("impl_tee", impl_tee)
}

func impl_tee() android.Module {
    module := cc.DefaultsFactory()
    android.AddLoadHook(module, impl_tee_hook)
    return module
}

func impl_tee_hook(ctx android.LoadHookContext) {
    ps := &props{}

    if ctx.AConfig().Getenv("MTK_TEE_GP_SUPPORT") == "yes" {
        ps.Cflags = append(ps.Cflags, "-DMTK_TRUSTONIC_TEE")
        ps.Shared_libs = append(ps.Shared_libs, "libMcClient")
        ps.Srcs = append(ps.Srcs, "src/TmsSePhControl.cpp")
        fmt.Println("impl_tee_hook: enable mtk tee gp support")
    } else {
        ps.Shared_libs = append(ps.Shared_libs, "libGPTEE_vendor")
        fmt.Println("impl_tee_hook: enable qcom tee gp support")
    }

    ctx.AppendProperties(ps)
}