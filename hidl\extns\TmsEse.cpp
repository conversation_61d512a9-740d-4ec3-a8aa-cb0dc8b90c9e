/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "TmsEse.h"
#include "TmsSeAdaptation.h"
#include "TmsSeUpdate.h"
#ifdef MTK_TRUSTONIC_TEE
#include "TmsSePhControl.h"
#endif

#ifdef TAG
#undef TAG
#endif
#define TAG    "TmsEse"

namespace vendor {
namespace tms {
namespace tmsese {
namespace V1_1 {
namespace implementation {

Return<int16_t> TmsEse::doAction(uint64_t ioctlType) {
    TMSLOG_D("%s: Enter", __func__);
    UNUSED(ioctlType);
    return (int16_t)TMS_SUCCESS;
}

Return<int16_t> TmsEse::sInit() {
    TMSLOG_D("%s: Enter", __func__);
    return (int16_t)TMS_SUCCESS;
}

Return<int16_t> TmsEse::sDeinit() {
    TMSLOG_D("%s: Enter", __func__);
    return (int16_t)TMS_SUCCESS;
}

Return<int16_t> TmsEse::sReset() {
    TMSLOG_D("%s: Enter", __func__);
    return (int16_t)TMS_SUCCESS;
}

Return<void> TmsEse::transmit(const android::hardware::hidl_vec<uint8_t> &data,
                              transmit_cb _hidl_cb) {
    TMSLOG_D("%s: Enter", __func__);
    android::hardware::hidl_vec<uint8_t> result;
    UNUSED(data);
    _hidl_cb(result);
    return Void();
}

Return<void> TmsEse::sGetAtr(sGetAtr_cb _hidl_cb) {
    TMSLOG_D("%s: Enter", __func__);
    hidl_vec<uint8_t> response;
    _hidl_cb(response);
    return Void();
}

Return<bool> TmsEse::setMtkSpiClk(bool enable) {
    TMSLOG_D("%s: Enter", __func__);
#ifdef MTK_TRUSTONIC_TEE
    static uint32_t externSpiCnt = 0;
    bool result = false;

    TMSLOG_I("%s: enable = %d, externSpiCnt = %d", __func__, enable, externSpiCnt);

    /* externSpiCnt prevent the client from releasing spiclk multiple times,
     * causing the spiclk of the CA to be released and a dump occurs */
    if (externSpiCnt == 0 && !enable) {
        TMSLOG_D("%s: stop releasing spi clk", __func__);
        return true;
    }

    result = enable ? requestSpiClk() : releaseSpiClk();

    if (result) {
        enable ? externSpiCnt++ : externSpiCnt--;
    }

    return result;
#else
    return enable ? enable : !enable;
#endif
}

Return<void> TmsEse::generic(uint32_t cmd,
                             const hidl_vec<uint8_t> &inData,
                             generic_cb _hidl_cb) {
    hidl_vec<uint8_t> outData;
    int status;
    UNUSED(inData);

    TMSLOG_D("%s: Enter", __func__);

    switch (cmd) {
        case TMS_SE_GENERIC_HARD_RESET: {
                status = tmsSeHardReset();
                break;
            }

        case TMS_SE_GENERIC_UPDATE_COS: {
                status = (tmsSeCheckCosUpdate() == DLStatus::OK) ? TMS_SUCCESS : TMS_FAILED;
                break;
            }

        case TMS_SE_GENERIC_UPDATE_PATCH: {
                status = (tmsSeCheckCosPatchUpdate() == DLStatus::OK) ? TMS_SUCCESS : TMS_FAILED;
                break;
            }

        case TMS_SE_GENERIC_UPDATE_APPLET: {
                status = (tmsSeCheckAppletUpdate() == DLStatus::OK) ? TMS_SUCCESS : TMS_FAILED;
                break;
            }

        default: {
                TMSLOG_W("%s : invalid cmd", __func__);
                status = TMS_FAILED;
                break;
            }
    }

    _hidl_cb(outData, status);
    TMSLOG_I("%s: cmd = %u, status = %d", __func__, cmd, status);
    return Void();
}

}  // namespace implementation
}  // namespace V1_1
}  // namespace tmsese
}  // namespace tms
}  // namespace vendor
