// Copyright (C) 2021-2022 Tsingteng MicroSystem
//
// All rights are reserved. Reproduction in whole or in part is
// prohibited without the written consent of the copyright owner.
//
// Tsingteng reserves the right to make changes without notice at any time.
//
// Tsingteng makes no warranty, expressed, implied or statutory, including but
// not limited to any implied warranty of merchantability or fitness for any
// particular purpose, or that the use will not infringe any third party patent,
// copyright or trademark. Tsingteng must not be liable for any loss or damage
// arising from its use.

//bootstrap_go_package {
//    name: "soong-impl_tee",
//    pkgPath: "android/soong/impl_tee",
//    deps: [
//        "soong-android",
//        "soong-cc",
//    ],
//    srcs: [
//        "impl_tee.go",
//    ],
//    pluginFor: ["soong_build"],
//}
//
//impl_tee {
//    name: "impl_tee",
//}

cc_library_shared {

    name: "libtms_seimpl_tee",
    vendor: true,
    owner: "tms",

    header_libs: [
        "securemsm_GPTEE_inc",
    ],

    //include_dirs: [
    //    "vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/ClientLib/include/GP",
    //],

    srcs: [
        "src/TmsSeAdaptation.cpp",
        "src/TmsSeGpApi.cpp",
        "src/TmsSePlatProp.cpp",
        "src/TmsSeGetConfig.cpp",
        "extns/src/TmsSeFeatures.cpp",
        "extns/src/TmsSeUpdate.cpp",
    ],

    local_include_dirs: [
        "inc",
        "extns/inc",
    ],

    shared_libs: [
        "libc",
        "libcutils",
        "liblog",
        "libdl",
        "libutils",
        "libhardware",
        "libhidlbase",
        "libbase",
        "libGPTEE_vendor",
        "libbinder_ndk",
        "android.hardware.secure_element-V1-ndk",
        //"android.hardware.secure_element@1.0", //hidl
    ],

    static_libs: [
        "libtms_base",
        "libtms_cosdl_aidl",
        //"libtms_cosdl_hidl",
        "libtms_cosdl_trad",
    ],

    cflags: [
        "-Wno-error=date-time",
        "-Wno-date-time",
        //"-DMTK_TRUSTONIC_TEE",
    ],
}
