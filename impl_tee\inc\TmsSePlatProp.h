/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#ifndef _TMS_SE_PLATPROP_H_
#define _TMS_SE_PLATPROP_H_

#include <stdint.h>
#include <string>
#include "TmsSeGetConfig.h"

typedef enum {
    QCOM,
    MTK,
    UNISOC,
    CUSTOM,
    UNDEFINED = 0xFF,
} PlatType;

typedef struct {
    uint8_t  clkPolarity;
    uint8_t  clkPhase;
    uint8_t  csPolarity;
    uint8_t  csMode;
    uint8_t  clkAlwaysOn;
    uint8_t  bitsPerWord;
} QcomProp;

typedef struct {
    uint32_t setuptime;
    uint32_t holdtime;
    uint32_t high_time;
    uint32_t low_time;
    uint32_t cs_idletime;
    uint32_t ulthgh_thrsh;
    uint8_t cpol;
    uint8_t cpha;
    uint8_t tx_mlsb;
    uint8_t rx_mlsb;
    uint8_t tx_endian;
    uint8_t rx_endian;
    uint8_t com_mod;
    uint8_t pause;
    uint8_t finish_intr;
    uint8_t deassert;
    uint8_t ulthigh;
    uint8_t tckdly;
} MtkProp;

typedef struct {
    uint8_t  DevId;
    uint32_t spiAdjust;
    uint32_t spiFreqMinLimit;
    uint32_t spiFreqMaxLimit;
    union {
        QcomProp qcom;
        MtkProp mtk;
    };
} PlatProp;

PlatProp getQcomPropFromConfig();
PlatProp getMtkPropFromConfig();

#endif