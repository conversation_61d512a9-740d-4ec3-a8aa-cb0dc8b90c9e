/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#ifndef _TMS_SE_UPDATE_
#define _TMS_SE_UPDATE_

#include <algorithm>
#include <dirent.h>
#include <chrono>
#include "tms_cosdl_api.h"

#define TMS_UPDATE_PATH_NAME_LEN_MAX (64)
DLStatus tmsSeCheckCosUpdate();
DLStatus tmsSeCheckCosPatchUpdate();
DLStatus tmsSeCheckAppletUpdate();
#endif