/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "TestCommon.h"
#include "TestUtils.h"
#include "MockTeeContext.h"
#include "TmsSeGetConfig.h"

using ::testing::_;
using ::testing::Return;
using ::testing::ReturnRef;

/**
 * TmsSeGetConfig模块的单元测试类
 */
class TmsSeGetConfigTest : public MockTestFixture {
protected:
    void SetUp() override {
        MockTestFixture::SetUp();
        setupDefaultMockBehavior();
        createTestConfigFile();
        LOG_TEST_INFO("TmsSeGetConfigTest setup completed");
    }
    
    void TearDown() override {
        // 清理配置
        EseConfig::clear();
        cleanupTestConfigFile();
        MockTestFixture::TearDown();
        LOG_TEST_INFO("TmsSeGetConfigTest teardown completed");
    }
    
private:
    void setupDefaultMockBehavior() {
        // 设置配置管理器的默认行为
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), hasKey(_))
            .WillRepeatedly(Return(true));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(_, _))
            .WillRepeatedly(Return("default_value"));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned(_, _))
            .WillRepeatedly(Return(0));
    }
    
    void createTestConfigFile() {
        std::string configContent = 
            "# TMS SE Test Configuration\n"
            "TMS_HAL_LOG_LEVEL=3\n"
            "TMS_TA_LOG_LEVEL=2\n"
            "TMS_ESE_DEV_NODE=/dev/tms_ese\n"
            "ESE_PROP_PLAT_TYPE=QCOM\n"
            "ESE_PROP_SPI_DEVICE_ID=1\n"
            "ESE_PROP_SPI_ADJUST_FREQ=1000000\n"
            "ESE_PROP_SPI_FREQ_MIN_LIMIT=100000\n"
            "ESE_PROP_SPI_FREQ_MAX_LIMIT=10000000\n"
            "QTEE_ESE_PROP_SPI_CLK_POLARITY=0\n"
            "QTEE_ESE_PROP_SPI_SHIFT_MODE=0\n"
            "QTEE_ESE_PROP_SPI_CS_POLARITY=0\n"
            "QTEE_ESE_PROP_SPI_CS_MODE=0\n"
            "QTEE_ESE_PROP_SPI_CLK_ALWAYS_ON=0\n"
            "QTEE_ESE_PROP_SPI_BITS_PER_WORD=8\n";
        
        TestUtils::createTestConfigFile("libese-tms.conf", configContent);
    }
    
    void cleanupTestConfigFile() {
        TestUtils::deleteTestConfigFile("libese-tms.conf");
    }
};

/**
 * 测试EseConfig类的基本功能
 */
TEST_F(TmsSeGetConfigTest, TestEseConfig_HasKey_Success) {
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), hasKey("TMS_HAL_LOG_LEVEL"))
        .WillOnce(Return(true));
    
    bool hasKey = EseConfig::hasKey("TMS_HAL_LOG_LEVEL");
    EXPECT_TRUE(hasKey);
}

TEST_F(TmsSeGetConfigTest, TestEseConfig_HasKey_NotFound) {
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), hasKey("NON_EXISTENT_KEY"))
        .WillOnce(Return(false));
    
    bool hasKey = EseConfig::hasKey("NON_EXISTENT_KEY");
    EXPECT_FALSE(hasKey);
}

TEST_F(TmsSeGetConfigTest, TestEseConfig_GetString_Success) {
    std::string expectedValue = "/dev/tms_ese";
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString("TMS_ESE_DEV_NODE", _))
        .WillOnce(Return(expectedValue));
    
    std::string value = EseConfig::getString("TMS_ESE_DEV_NODE");
    EXPECT_EQ(expectedValue, value);
}

TEST_F(TmsSeGetConfigTest, TestEseConfig_GetString_WithDefault) {
    std::string defaultValue = "/dev/default_ese";
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString("NON_EXISTENT_KEY", defaultValue))
        .WillOnce(Return(defaultValue));
    
    std::string value = EseConfig::getString("NON_EXISTENT_KEY", defaultValue);
    EXPECT_EQ(defaultValue, value);
}

TEST_F(TmsSeGetConfigTest, TestEseConfig_GetUnsigned_Success) {
    unsigned int expectedValue = 3;
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("TMS_HAL_LOG_LEVEL", _))
        .WillOnce(Return(expectedValue));
    
    unsigned int value = EseConfig::getUnsigned("TMS_HAL_LOG_LEVEL");
    EXPECT_EQ(expectedValue, value);
}

TEST_F(TmsSeGetConfigTest, TestEseConfig_GetUnsigned_WithDefault) {
    unsigned int defaultValue = 1;
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("NON_EXISTENT_KEY", defaultValue))
        .WillOnce(Return(defaultValue));
    
    unsigned int value = EseConfig::getUnsigned("NON_EXISTENT_KEY", defaultValue);
    EXPECT_EQ(defaultValue, value);
}

TEST_F(TmsSeGetConfigTest, TestEseConfig_GetBytes_Success) {
    std::vector<uint8_t> expectedValue = {0x01, 0x02, 0x03, 0x04};
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getBytes("TEST_BYTES_KEY"))
        .WillOnce(Return(expectedValue));
    
    std::vector<uint8_t> value = EseConfig::getBytes("TEST_BYTES_KEY");
    EXPECT_VECTOR_EQ(expectedValue, value);
}

TEST_F(TmsSeGetConfigTest, TestEseConfig_GetBytes_Empty) {
    std::vector<uint8_t> emptyValue;
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getBytes("EMPTY_BYTES_KEY"))
        .WillOnce(Return(emptyValue));
    
    std::vector<uint8_t> value = EseConfig::getBytes("EMPTY_BYTES_KEY");
    EXPECT_TRUE(value.empty());
}

/**
 * 测试C接口函数
 */
TEST_F(TmsSeGetConfigTest, TestConfigGetUnsigned_Success) {
    const char* key = "TMS_HAL_LOG_LEVEL";
    unsigned int defaultVal = 1;
    unsigned int expectedVal = 3;
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned(std::string(key), defaultVal))
        .WillOnce(Return(expectedVal));
    
    unsigned int value = ConfigGetUnsigned(key, strlen(key), defaultVal);
    EXPECT_EQ(expectedVal, value);
}

TEST_F(TmsSeGetConfigTest, TestConfigGetUnsigned_UseDefault) {
    const char* key = "NON_EXISTENT_KEY";
    unsigned int defaultVal = 999;
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned(std::string(key), defaultVal))
        .WillOnce(Return(defaultVal));
    
    unsigned int value = ConfigGetUnsigned(key, strlen(key), defaultVal);
    EXPECT_EQ(defaultVal, value);
}

TEST_F(TmsSeGetConfigTest, TestConfigGetString_Success) {
    const char* key = "TMS_ESE_DEV_NODE";
    const char* defaultVal = "/dev/default";
    const char* expectedVal = "/dev/tms_ese";
    char buffer[256];
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(std::string(key), std::string(defaultVal)))
        .WillOnce(Return(std::string(expectedVal)));
    
    ConfigGetString(buffer, sizeof(buffer), key, strlen(key), defaultVal, strlen(defaultVal));
    EXPECT_STREQ(expectedVal, buffer);
}

TEST_F(TmsSeGetConfigTest, TestConfigGetString_UseDefault) {
    const char* key = "NON_EXISTENT_KEY";
    const char* defaultVal = "/dev/default";
    char buffer[256];
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(std::string(key), std::string(defaultVal)))
        .WillOnce(Return(std::string(defaultVal)));
    
    ConfigGetString(buffer, sizeof(buffer), key, strlen(key), defaultVal, strlen(defaultVal));
    EXPECT_STREQ(defaultVal, buffer);
}

TEST_F(TmsSeGetConfigTest, TestConfigGetString_BufferTooSmall) {
    const char* key = "TMS_ESE_DEV_NODE";
    const char* defaultVal = "/dev/default";
    const char* expectedVal = "/dev/very_long_device_node_name_that_exceeds_buffer_size";
    char buffer[10]; // 故意设置很小的缓冲区
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(std::string(key), std::string(defaultVal)))
        .WillOnce(Return(std::string(expectedVal)));
    
    ConfigGetString(buffer, sizeof(buffer), key, strlen(key), defaultVal, strlen(defaultVal));
    
    // 应该被截断，但不会崩溃
    EXPECT_LT(strlen(buffer), strlen(expectedVal));
    EXPECT_EQ('\0', buffer[sizeof(buffer) - 1]); // 确保以null结尾
}

/**
 * 测试配置清理功能
 */
TEST_F(TmsSeGetConfigTest, TestEseConfig_Clear) {
    // 先获取一些配置值
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(_, _))
        .WillRepeatedly(Return("test_value"));
    
    std::string value1 = EseConfig::getString("TEST_KEY1");
    std::string value2 = EseConfig::getString("TEST_KEY2");
    
    EXPECT_EQ("test_value", value1);
    EXPECT_EQ("test_value", value2);
    
    // 清理配置
    EseConfig::clear();
    
    // 清理后应该重新加载配置
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(_, _))
        .WillRepeatedly(Return("new_value"));
    
    std::string value3 = EseConfig::getString("TEST_KEY3");
    EXPECT_EQ("new_value", value3);
}

/**
 * 测试特定配置项
 */
TEST_F(TmsSeGetConfigTest, TestSpecificConfigKeys) {
    // 测试日志级别配置
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned(NAME_TMS_HAL_LOG_LEVEL, _))
        .WillOnce(Return(3));
    
    unsigned int logLevel = EseConfig::getUnsigned(NAME_TMS_HAL_LOG_LEVEL);
    EXPECT_EQ(3u, logLevel);
    
    // 测试设备节点配置
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(NAME_TMS_ESE_DEV_NODE, _))
        .WillOnce(Return("/dev/tms_ese"));
    
    std::string devNode = EseConfig::getString(NAME_TMS_ESE_DEV_NODE);
    EXPECT_EQ("/dev/tms_ese", devNode);
    
    // 测试平台类型配置
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(NAME_ESE_PROP_PLAT_TYPE, _))
        .WillOnce(Return("QCOM"));
    
    std::string platType = EseConfig::getString(NAME_ESE_PROP_PLAT_TYPE);
    EXPECT_EQ("QCOM", platType);
}

/**
 * 参数化测试：测试不同的配置键值对
 */
class TmsSeGetConfigParameterizedTest : public TmsSeGetConfigTest,
                                       public ::testing::WithParamInterface<std::pair<std::string, std::string>> {
};

TEST_P(TmsSeGetConfigParameterizedTest, TestConfigKeyValuePairs) {
    auto param = GetParam();
    std::string key = param.first;
    std::string expectedValue = param.second;
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getString(key, _))
        .WillOnce(Return(expectedValue));
    
    std::string actualValue = EseConfig::getString(key);
    EXPECT_EQ(expectedValue, actualValue);
}

INSTANTIATE_TEST_SUITE_P(
    ConfigKeyValueTests,
    TmsSeGetConfigParameterizedTest,
    ::testing::Values(
        std::make_pair(NAME_TMS_ESE_DEV_NODE, "/dev/tms_ese"),
        std::make_pair(NAME_ESE_PROP_PLAT_TYPE, "QCOM"),
        std::make_pair(NAME_QTEE_ESE_PROP_SPI_CLK_POLARITY, "0"),
        std::make_pair(NAME_QTEE_ESE_PROP_SPI_SHIFT_MODE, "0"),
        std::make_pair(NAME_MTK_ESE_PROP_SPI_SETUP_TIME, "2184"),
        std::make_pair(NAME_MTK_ESE_PROP_SPI_HOLD_TIME, "110")
    )
);
