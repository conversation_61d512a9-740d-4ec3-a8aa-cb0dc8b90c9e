# Copyright (C) 2021-2022 Tsingteng MicroSystem
#
# All rights are reserved. Reproduction in whole or in part is
# prohibited without the written consent of the copyright owner.
#
# Tsingteng reserves the right to make changes without notice at any time.
#
# Tsingteng makes no warranty, expressed, implied or statutory, including but
# not limited to any implied warranty of merchantability or fitness for any
# particular purpose, or that the use will not infringe any third party patent,
# copyright or trademark. Tsingteng must not be liable for any loss or damage
# arising from its use.

LOCAL_PATH:= $(call my-dir)
ifeq ($(strip $(TMS_ESE_PRODUCT)),true)
include $(CLEAR_VARS)

$(info MTK_TEE_GP_SUPPORT = $(MTK_TEE_GP_SUPPORT))

LOCAL_MODULE_RELATIVE_PATH := hw
LOCAL_VENDOR_MODULE := true

LOCAL_MODULE := android.hardware.secure_element-service-tms
ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
    ifeq ($(strip $(TMS_SE_AIDL_HAL_SUPPORT)),true)
        LOCAL_INIT_RC := secure_element-optee-service-tms.rc
    endif
else
LOCAL_INIT_RC := secure_element-service-tms.rc
endif

LOCAL_VINTF_FRAGMENTS := secure_element-service-tms.xml

LOCAL_SRC_FILES := \
    SEService.cpp \
    SecureElement.cpp \
    extns/TmsEse.cpp

LOCAL_SHARED_LIBRARIES := \
    android.hardware.secure_element-V1-ndk \
    libbinder_ndk \
    libbase \
    libcutils \
    libhardware \
    libhidlbase \
    liblog \
    libutils \
    libtms_seimpl_tee \
    vendor.tms.tmsese_aidl-V1-ndk \
    libtms_log_record

LOCAL_STATIC_LIBRARIES := \
    libtms_base \
    libtms_cosdl_aidl \
    libtms_cosdl_trad

LOCAL_C_INCLUDES := \
    $(LOCAL_PATH)/extns \
    $(LOCAL_PATH)/../impl_tee/inc \
    $(LOCAL_PATH)/../impl_tee/extns/inc

#LOCAL_CFLAGS += -DCHECK_UNION_PARAM

ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
LOCAL_SHARED_LIBRARIES += libteec
endif


ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
LOCAL_CFLAGS += -DENABLE_OPTEE_FUNC
LOCAL_C_INCLUDES += \
    vendor/optee/optee_client/libteec/include
else ifeq ($(strip $(MTK_TEE_GP_SUPPORT)), yes)
LOCAL_CFLAGS += -DMTK_TRUSTONIC_TEE
LOCAL_C_INCLUDES += \
    vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/ClientLib/include/GP
else
LOCAL_C_INCLUDES += \
    vendor/qcom/proprietary/securemsm/GPTEE/inc
endif
include $(BUILD_EXECUTABLE)
endif

ifeq ($(strip $(TMS_SE_INDEPT_PRODUCT)),true)
include $(CLEAR_VARS)

$(info MTK_TEE_GP_SUPPORT = $(MTK_TEE_GP_SUPPORT))

LOCAL_MODULE_RELATIVE_PATH := hw
LOCAL_VENDOR_MODULE := true

LOCAL_MODULE := android.hardware.secure_element-service-tms-indept
ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
    ifeq ($(strip $(TMS_SE_AIDL_HAL_SUPPORT)),true)
        LOCAL_INIT_RC := secure_element-optee-service-tms-indept.rc
    endif
else
LOCAL_INIT_RC := secure_element-service-tms-indept.rc
endif

LOCAL_VINTF_FRAGMENTS := secure_element-service-tms.xml

LOCAL_SRC_FILES := \
    SEService.cpp \
    SecureElement.cpp \
    extns/TmsEse.cpp

LOCAL_SHARED_LIBRARIES := \
    android.hardware.secure_element-V1-ndk \
    libbinder_ndk \
    libbase \
    libcutils \
    libhardware \
    libhidlbase \
    liblog \
    libutils \
    libtms_seimpl_tee \
    vendor.tms.tmsese_aidl-V1-ndk \
    libtms_log_record

LOCAL_STATIC_LIBRARIES := \
    libtms_base \
    libtms_cosdl_aidl \
    libtms_cosdl_trad

LOCAL_C_INCLUDES := \
    $(LOCAL_PATH)/extns \
    $(LOCAL_PATH)/../impl_tee/inc \
    $(LOCAL_PATH)/../impl_tee/extns/inc

#LOCAL_CFLAGS += -DCHECK_UNION_PARAM
LOCAL_CFLAGS += -DSE_INDEPT_SUPPORT

ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
LOCAL_SHARED_LIBRARIES += libteec
endif


ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
LOCAL_CFLAGS += -DENABLE_OPTEE_FUNC
LOCAL_C_INCLUDES += \
    vendor/optee/optee_client/libteec/include
else ifeq ($(strip $(MTK_TEE_GP_SUPPORT)), yes)
LOCAL_CFLAGS += -DMTK_TRUSTONIC_TEE
LOCAL_C_INCLUDES += \
    vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/ClientLib/include/GP
else
LOCAL_C_INCLUDES += \
    vendor/qcom/proprietary/securemsm/GPTEE/inc
endif

include $(BUILD_EXECUTABLE)
endif

include $(call all-makefiles-under,$(LOCAL_PATH))
