/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "TestCommon.h"
#include "TestUtils.h"
#include "MockTeeContext.h"
#include "TmsSePlatProp.h"

using ::testing::_;
using ::testing::Return;

/**
 * TmsSePlatProp模块的单元测试类
 */
class TmsSePlatPropTest : public MockTestFixture {
protected:
    void SetUp() override {
        MockTestFixture::SetUp();
        setupDefaultMockBehavior();
        LOG_TEST_INFO("TmsSePlatPropTest setup completed");
    }
    
    void TearDown() override {
        MockTestFixture::TearDown();
        LOG_TEST_INFO("TmsSePlatPropTest teardown completed");
    }
    
private:
    void setupDefaultMockBehavior() {
        // 设置配置管理器的默认行为
        setupQcomConfigMocks();
        setupMtkConfigMocks();
    }
    
    void setupQcomConfigMocks() {
        // QCOM平台相关配置的Mock设置
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("ESE_PROP_SPI_DEVICE_ID", _))
            .WillRepeatedly(Return(1));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("ESE_PROP_SPI_ADJUST_FREQ", _))
            .WillRepeatedly(Return(1000000));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("ESE_PROP_SPI_FREQ_MIN_LIMIT", _))
            .WillRepeatedly(Return(100000));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("ESE_PROP_SPI_FREQ_MAX_LIMIT", _))
            .WillRepeatedly(Return(10000000));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CLK_POLARITY", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_SHIFT_MODE", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CS_POLARITY", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CS_MODE", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CLK_ALWAYS_ON", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_BITS_PER_WORD", _))
            .WillRepeatedly(Return(8));
    }
    
    void setupMtkConfigMocks() {
        // MTK平台相关配置的Mock设置
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_SPI_SETUP_TIME", _))
            .WillRepeatedly(Return(2184));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_SPI_HOLD_TIME", _))
            .WillRepeatedly(Return(110));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_HIGH_TIME", _))
            .WillRepeatedly(Return(10));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_LOW_TIME", _))
            .WillRepeatedly(Return(10));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_CS_IDLE_TIME", _))
            .WillRepeatedly(Return(110));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_ULTHGH_THRSH", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_CPOL", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_CPHA", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_TX_MLSB", _))
            .WillRepeatedly(Return(1));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_RX_MLSB", _))
            .WillRepeatedly(Return(1));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_TX_ENDIAN", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_RX_ENDIAN", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_COM_MOD", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_PAUSE", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_FINISH_INTR", _))
            .WillRepeatedly(Return(1));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_DEASSERT", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_ULTHIGH", _))
            .WillRepeatedly(Return(0));
        
        EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_TCKDLY", _))
            .WillRepeatedly(Return(0));
    }
};

/**
 * 测试QCOM平台属性获取
 */
TEST_F(TmsSePlatPropTest, TestGetQcomPropFromConfig_Success) {
    PlatProp prop = getQcomPropFromConfig();
    
    // 验证基本属性
    EXPECT_EQ(1u, prop.DevId);
    EXPECT_EQ(1000000u, prop.spiAdjust);
    EXPECT_EQ(100000u, prop.spiFreqMinLimit);
    EXPECT_EQ(10000000u, prop.spiFreqMaxLimit);
    
    // 验证QCOM特有属性
    EXPECT_EQ(0u, prop.qcom.clkPolarity);
    EXPECT_EQ(0u, prop.qcom.clkPhase);
    EXPECT_EQ(0u, prop.qcom.csPolarity);
    EXPECT_EQ(0u, prop.qcom.csMode);
    EXPECT_EQ(0u, prop.qcom.clkAlwaysOn);
    EXPECT_EQ(8u, prop.qcom.bitsPerWord);
}

TEST_F(TmsSePlatPropTest, TestGetQcomPropFromConfig_CustomValues) {
    // 设置自定义配置值
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("ESE_PROP_SPI_DEVICE_ID", _))
        .WillOnce(Return(2));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("ESE_PROP_SPI_ADJUST_FREQ", _))
        .WillOnce(Return(2000000));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CLK_POLARITY", _))
        .WillOnce(Return(1));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_SHIFT_MODE", _))
        .WillOnce(Return(1));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_BITS_PER_WORD", _))
        .WillOnce(Return(16));
    
    PlatProp prop = getQcomPropFromConfig();
    
    EXPECT_EQ(2u, prop.DevId);
    EXPECT_EQ(2000000u, prop.spiAdjust);
    EXPECT_EQ(1u, prop.qcom.clkPolarity);
    EXPECT_EQ(1u, prop.qcom.clkPhase);
    EXPECT_EQ(16u, prop.qcom.bitsPerWord);
}

/**
 * 测试MTK平台属性获取
 */
TEST_F(TmsSePlatPropTest, TestGetMtkPropFromConfig_Success) {
    PlatProp prop = getMtkPropFromConfig();
    
    // 验证基本属性
    EXPECT_EQ(1u, prop.DevId);
    EXPECT_EQ(1000000u, prop.spiAdjust);
    EXPECT_EQ(100000u, prop.spiFreqMinLimit);
    EXPECT_EQ(10000000u, prop.spiFreqMaxLimit);
    
    // 验证MTK特有属性
    EXPECT_EQ(2184u, prop.mtk.setuptime);
    EXPECT_EQ(110u, prop.mtk.holdtime);
    EXPECT_EQ(10u, prop.mtk.high_time);
    EXPECT_EQ(10u, prop.mtk.low_time);
    EXPECT_EQ(110u, prop.mtk.cs_idletime);
    EXPECT_EQ(0u, prop.mtk.ulthgh_thrsh);
    EXPECT_EQ(0u, prop.mtk.cpol);
    EXPECT_EQ(0u, prop.mtk.cpha);
    EXPECT_EQ(1u, prop.mtk.tx_mlsb);
    EXPECT_EQ(1u, prop.mtk.rx_mlsb);
    EXPECT_EQ(0u, prop.mtk.tx_endian);
    EXPECT_EQ(0u, prop.mtk.rx_endian);
    EXPECT_EQ(0u, prop.mtk.com_mod);
    EXPECT_EQ(0u, prop.mtk.pause);
    EXPECT_EQ(1u, prop.mtk.finish_intr);
    EXPECT_EQ(0u, prop.mtk.deassert);
    EXPECT_EQ(0u, prop.mtk.ulthigh);
    EXPECT_EQ(0u, prop.mtk.tckdly);
}

TEST_F(TmsSePlatPropTest, TestGetMtkPropFromConfig_CustomValues) {
    // 设置自定义配置值
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_SPI_SETUP_TIME", _))
        .WillOnce(Return(3000));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_SPI_HOLD_TIME", _))
        .WillOnce(Return(200));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_HIGH_TIME", _))
        .WillOnce(Return(20));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_LOW_TIME", _))
        .WillOnce(Return(20));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_CPOL", _))
        .WillOnce(Return(1));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_CPHA", _))
        .WillOnce(Return(1));
    
    PlatProp prop = getMtkPropFromConfig();
    
    EXPECT_EQ(3000u, prop.mtk.setuptime);
    EXPECT_EQ(200u, prop.mtk.holdtime);
    EXPECT_EQ(20u, prop.mtk.high_time);
    EXPECT_EQ(20u, prop.mtk.low_time);
    EXPECT_EQ(1u, prop.mtk.cpol);
    EXPECT_EQ(1u, prop.mtk.cpha);
}

/**
 * 测试默认值处理
 */
TEST_F(TmsSePlatPropTest, TestGetQcomPropFromConfig_DefaultValues) {
    // 清除所有Mock期望，使用默认值
    ::testing::Mock::VerifyAndClearExpectations(MOCK_CONFIG_MANAGER());
    
    // 设置返回默认值的行为
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned(_, _))
        .WillRepeatedly([](const std::string& key, unsigned int defaultValue) {
            return defaultValue;
        });
    
    PlatProp prop = getQcomPropFromConfig();
    
    // 验证使用了默认值
    EXPECT_EQ(1u, prop.DevId); // 默认设备ID
    EXPECT_EQ(1000000u, prop.spiAdjust); // 默认频率
}

TEST_F(TmsSePlatPropTest, TestGetMtkPropFromConfig_DefaultValues) {
    // 清除所有Mock期望，使用默认值
    ::testing::Mock::VerifyAndClearExpectations(MOCK_CONFIG_MANAGER());
    
    // 设置返回默认值的行为
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned(_, _))
        .WillRepeatedly([](const std::string& key, unsigned int defaultValue) {
            return defaultValue;
        });
    
    PlatProp prop = getMtkPropFromConfig();
    
    // 验证使用了默认值
    EXPECT_EQ(2184u, prop.mtk.setuptime); // 默认setup time
    EXPECT_EQ(110u, prop.mtk.holdtime); // 默认hold time
}

/**
 * 测试边界值
 */
TEST_F(TmsSePlatPropTest, TestQcomPropBoundaryValues) {
    // 测试最大值
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("ESE_PROP_SPI_DEVICE_ID", _))
        .WillOnce(Return(UINT32_MAX));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("ESE_PROP_SPI_ADJUST_FREQ", _))
        .WillOnce(Return(UINT32_MAX));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_BITS_PER_WORD", _))
        .WillOnce(Return(255)); // uint8_t最大值
    
    PlatProp prop = getQcomPropFromConfig();
    
    EXPECT_EQ(UINT32_MAX, prop.DevId);
    EXPECT_EQ(UINT32_MAX, prop.spiAdjust);
    EXPECT_EQ(255u, prop.qcom.bitsPerWord);
}

TEST_F(TmsSePlatPropTest, TestMtkPropBoundaryValues) {
    // 测试最小值
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_SPI_SETUP_TIME", _))
        .WillOnce(Return(0));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_SPI_HOLD_TIME", _))
        .WillOnce(Return(0));
    
    // 测试最大值
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_HIGH_TIME", _))
        .WillOnce(Return(UINT32_MAX));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("MTK_ESE_PROP_LOW_TIME", _))
        .WillOnce(Return(UINT32_MAX));
    
    PlatProp prop = getMtkPropFromConfig();
    
    EXPECT_EQ(0u, prop.mtk.setuptime);
    EXPECT_EQ(0u, prop.mtk.holdtime);
    EXPECT_EQ(UINT32_MAX, prop.mtk.high_time);
    EXPECT_EQ(UINT32_MAX, prop.mtk.low_time);
}

/**
 * 参数化测试：测试不同的配置组合
 */
struct QcomConfigTestParam {
    uint8_t clkPolarity;
    uint8_t clkPhase;
    uint8_t csPolarity;
    uint8_t csMode;
    uint8_t clkAlwaysOn;
    uint8_t bitsPerWord;
};

class TmsSePlatPropQcomParameterizedTest : public TmsSePlatPropTest,
                                          public ::testing::WithParamInterface<QcomConfigTestParam> {
};

TEST_P(TmsSePlatPropQcomParameterizedTest, TestQcomConfigCombinations) {
    auto param = GetParam();
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CLK_POLARITY", _))
        .WillOnce(Return(param.clkPolarity));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_SHIFT_MODE", _))
        .WillOnce(Return(param.clkPhase));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CS_POLARITY", _))
        .WillOnce(Return(param.csPolarity));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CS_MODE", _))
        .WillOnce(Return(param.csMode));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_CLK_ALWAYS_ON", _))
        .WillOnce(Return(param.clkAlwaysOn));
    
    EXPECT_CALL(*MOCK_CONFIG_MANAGER(), getUnsigned("QTEE_ESE_PROP_SPI_BITS_PER_WORD", _))
        .WillOnce(Return(param.bitsPerWord));
    
    PlatProp prop = getQcomPropFromConfig();
    
    EXPECT_EQ(param.clkPolarity, prop.qcom.clkPolarity);
    EXPECT_EQ(param.clkPhase, prop.qcom.clkPhase);
    EXPECT_EQ(param.csPolarity, prop.qcom.csPolarity);
    EXPECT_EQ(param.csMode, prop.qcom.csMode);
    EXPECT_EQ(param.clkAlwaysOn, prop.qcom.clkAlwaysOn);
    EXPECT_EQ(param.bitsPerWord, prop.qcom.bitsPerWord);
}

INSTANTIATE_TEST_SUITE_P(
    QcomConfigTests,
    TmsSePlatPropQcomParameterizedTest,
    ::testing::Values(
        QcomConfigTestParam{0, 0, 0, 0, 0, 8},
        QcomConfigTestParam{1, 0, 0, 0, 0, 8},
        QcomConfigTestParam{0, 1, 0, 0, 0, 8},
        QcomConfigTestParam{0, 0, 1, 0, 0, 8},
        QcomConfigTestParam{0, 0, 0, 1, 0, 8},
        QcomConfigTestParam{0, 0, 0, 0, 1, 8},
        QcomConfigTestParam{1, 1, 1, 1, 1, 16},
        QcomConfigTestParam{1, 1, 1, 1, 1, 32}
    )
);
