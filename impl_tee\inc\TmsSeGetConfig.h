/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2021. All rights reserved.
 */
/******************************************************************************
 *  Copyright 2018-2020 NXP
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 ******************************************************************************/

/******************************************************************************
*
*  The original Work has been changed by Tsingteng MicroSystem.
*
*  Copyright (C) 2021-2022 Tsingteng MicroSystem
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*
*  NOT A CONTRIBUTION
******************************************************************************/

#ifndef TMS_SE_CONFIG_H
#define TMS_SE_CONFIG_H

#include "TmsConfig.h"

#define NAME_TMS_HAL_LOG_LEVEL "TMS_HAL_LOG_LEVEL"
#define NAME_TMS_TA_LOG_LEVEL "TMS_TA_LOG_LEVEL"
#define NAME_TMS_ESE_DEV_NODE "TMS_ESE_DEV_NODE"
#define NAME_TMS_ESE_UPDATE_COS "TMS_ESE_UPDATE_COS"
#define NAME_TMS_ESE_UPDATE_PATCH "TMS_ESE_UPDATE_PATCH"
#define NAME_TMS_ESE_UPDATE_APPLET "TMS_ESE_UPDATE_APPLET"
#define NAME_ESE_PROP_PLAT_TYPE "ESE_PROP_PLAT_TYPE"
#define NAME_ESE_PROP_SPI_DEVICE_ID "ESE_PROP_SPI_DEVICE_ID"
#define NAME_ESE_PROP_SPI_ADJUST_FREQ "ESE_PROP_SPI_ADJUST_FREQ"
#define NAME_ESE_PROP_SPI_FREQ_MIN_LIMIT "ESE_PROP_SPI_FREQ_MIN_LIMIT"
#define NAME_ESE_PROP_SPI_FREQ_MAX_LIMIT "ESE_PROP_SPI_FREQ_MAX_LIMIT"
// if use qtee
#define NAME_QTEE_ESE_PROP_SPI_CLK_POLARITY "QTEE_ESE_PROP_SPI_CLK_POLARITY"
#define NAME_QTEE_ESE_PROP_SPI_SHIFT_MODE "QTEE_ESE_PROP_SPI_SHIFT_MODE"
#define NAME_QTEE_ESE_PROP_SPI_CS_POLARITY "QTEE_ESE_PROP_SPI_CS_POLARITY"
#define NAME_QTEE_ESE_PROP_SPI_CS_MODE "QTEE_ESE_PROP_SPI_CS_MODE"
#define NAME_QTEE_ESE_PROP_SPI_CLK_ALWAYS_ON "QTEE_ESE_PROP_SPI_CLK_ALWAYS_ON"
#define NAME_QTEE_ESE_PROP_SPI_BITS_PER_WORD "QTEE_ESE_PROP_SPI_BITS_PER_WORD"

// for mtk
#define NAME_MTK_ESE_PROP_SPI_SETUP_TIME "MTK_ESE_PROP_SPI_SETUP_TIME"
#define NAME_MTK_ESE_PROP_SPI_HOLD_TIME "MTK_ESE_PROP_SPI_HOLD_TIME"
#define NAME_MTK_ESE_PROP_HIGH_TIME "MTK_ESE_PROP_HIGH_TIME"
#define NAME_MTK_ESE_PROP_LOW_TIME "MTK_ESE_PROP_LOW_TIME"
#define NAME_MTK_ESE_PROP_CS_IDLE_TIME "MTK_ESE_PROP_CS_IDLE_TIME"
#define NAME_MTK_ESE_PROP_ULTHGH_THRSH "MTK_ESE_PROP_ULTHGH_THRSH"
#define NAME_MTK_ESE_PROP_CPOL "MTK_ESE_PROP_CPOL"
#define NAME_MTK_ESE_PROP_CPHA "MTK_ESE_PROP_CPHA"
#define NAME_MTK_ESE_PROP_TX_MLSB "MTK_ESE_PROP_TX_MLSB"
#define NAME_MTK_ESE_PROP_RX_MLSB "MTK_ESE_PROP_RX_MLSB"
#define NAME_MTK_ESE_PROP_TX_ENDIAN "MTK_ESE_PROP_TX_ENDIAN"
#define NAME_MTK_ESE_PROP_RX_ENDIAN "MTK_ESE_PROP_RX_ENDIAN"
#define NAME_MTK_ESE_PROP_COM_MOD "MTK_ESE_PROP_COM_MOD"
#define NAME_MTK_ESE_PROP_PAUSE "MTK_ESE_PROP_PAUSE"
#define NAME_MTK_ESE_PROP_FINISH_INTR "MTK_ESE_PROP_FINISH_INTR"
#define NAME_MTK_ESE_PROP_DEASSERT "MTK_ESE_PROP_DEASSERT"
#define NAME_MTK_ESE_PROP_ULTHIGH "MTK_ESE_PROP_ULTHIGH"
#define NAME_MTK_ESE_PROP_TCKDLY "MTK_ESE_PROP_TCKDLY"

class EseConfig {
  public:
    ~EseConfig();
    static bool hasKey(const std::string &key);
    static std::string getString(const std::string &key);
    static std::string getString(const std::string &key,
                                 std::string default_value);
    static unsigned getUnsigned(const std::string &key);
    static unsigned getUnsigned(const std::string &key, unsigned default_value);
    static std::vector<uint8_t> getBytes(const std::string &key);
    static void clear();

  private:
    static EseConfig &getInstance();
    EseConfig();

    ConfigFile config_;
};


#ifdef __cplusplus
extern "C" {
#endif
unsigned int ConfigGetUnsigned(const char *key, int keyLen,
                               const unsigned int defaultVal);
void ConfigGetString(char *buff, int buffLen,
                     const char *key, int keyLen,
                     const char *defaultVal, int defaultValLen);
#ifdef __cplusplus
}
#endif

#endif /* TMS_SE_CONFIG_H */
