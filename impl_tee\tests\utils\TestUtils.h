/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#ifndef TMS_SE_TEST_UTILS_H
#define TMS_SE_TEST_UTILS_H

#include <vector>
#include <string>
#include <memory>
#include "TestCommon.h"

/**
 * 测试工具类
 * 提供测试中常用的辅助功能
 */
class TestUtils {
public:
    // 测试环境设置和清理
    static void setupTestEnvironment();
    static void cleanupTestEnvironment();
    
    // 数据转换工具
    static std::string vectorToHexString(const std::vector<uint8_t>& data);
    static std::vector<uint8_t> hexStringToVector(const std::string& hexStr);
    static bool isVectorEqual(const std::vector<uint8_t>& v1, const std::vector<uint8_t>& v2);
    
    // APDU工具
    static std::vector<uint8_t> createSelectApdu(const std::vector<uint8_t>& aid, uint8_t p2 = 0x00);
    static std::vector<uint8_t> createGetResponseApdu(uint8_t le = 0x00);
    static std::vector<uint8_t> createManageChannelApdu(uint8_t instruction, uint8_t channelNum = 0x00);
    static bool isSuccessResponse(const std::vector<uint8_t>& response);
    static uint16_t getStatusWord(const std::vector<uint8_t>& response);
    
    // 测试数据生成
    static std::vector<uint8_t> generateRandomData(size_t length);
    static std::vector<uint8_t> generateTestAid(uint8_t aidLength = 8);
    static std::vector<uint8_t> generateTestAtr();
    
    // 文件操作工具
    static bool createTestConfigFile(const std::string& filename, const std::string& content);
    static bool deleteTestConfigFile(const std::string& filename);
    static std::string getTestConfigPath();
    
    // 时间工具
    static void sleepMs(uint32_t milliseconds);
    static uint64_t getCurrentTimeMs();
    
    // 日志工具
    static void enableTestLogging();
    static void disableTestLogging();
    static void logTestInfo(const std::string& message);
    static void logTestError(const std::string& message);
    
    // 内存工具
    static void* allocateAlignedMemory(size_t size, size_t alignment = 16);
    static void freeAlignedMemory(void* ptr);
    
    // 平台检测
    static bool isMtkPlatform();
    static bool isQcomPlatform();
    static bool isOpteeSupported();
    
private:
    TestUtils() = default;
    ~TestUtils() = default;
    
    static bool s_testEnvironmentInitialized;
    static bool s_testLoggingEnabled;
};

/**
 * RAII风格的测试资源管理器
 */
template<typename T>
class TestResourceManager {
public:
    using CleanupFunc = std::function<void(T*)>;
    
    TestResourceManager(T* resource, CleanupFunc cleanup)
        : resource_(resource), cleanup_(cleanup) {}
    
    ~TestResourceManager() {
        if (resource_ && cleanup_) {
            cleanup_(resource_);
        }
    }
    
    T* get() const { return resource_; }
    T* release() {
        T* temp = resource_;
        resource_ = nullptr;
        return temp;
    }
    
private:
    T* resource_;
    CleanupFunc cleanup_;
};

// 便利宏定义
#define TEST_RESOURCE_MANAGER(type, var, resource, cleanup) \
    TestResourceManager<type> var(resource, cleanup)

#define LOG_TEST_INFO(msg) TestUtils::logTestInfo(msg)
#define LOG_TEST_ERROR(msg) TestUtils::logTestError(msg)

#define SLEEP_MS(ms) TestUtils::sleepMs(ms)

#endif // TMS_SE_TEST_UTILS_H
