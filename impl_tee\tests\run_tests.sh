#!/bin/bash

# Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.

# TMS SE impl_tee 单元测试运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 测试相关变量
TEST_BINARY="tms_seimpl_tee_unit_tests"
TEST_OUTPUT_DIR="/data/vendor/tms/test_results"
TEST_LOG_FILE="${TEST_OUTPUT_DIR}/test_log.txt"
TEST_XML_FILE="${TEST_OUTPUT_DIR}/test_results.xml"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

print_info() {
    print_message "${BLUE}" "INFO: $1"
}

print_success() {
    print_message "${GREEN}" "SUCCESS: $1"
}

print_warning() {
    print_message "${YELLOW}" "WARNING: $1"
}

print_error() {
    print_message "${RED}" "ERROR: $1"
}

# 函数：显示帮助信息
show_help() {
    echo "TMS SE impl_tee 单元测试运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -b, --build             构建测试"
    echo "  -r, --run               运行测试"
    echo "  -c, --clean             清理构建产物"
    echo "  -a, --all               构建并运行测试"
    echo "  -f, --filter PATTERN    运行匹配模式的测试"
    echo "  -v, --verbose           详细输出"
    echo "  -x, --xml               生成XML格式的测试报告"
    echo "  --coverage              生成代码覆盖率报告"
    echo ""
    echo "示例:"
    echo "  $0 --all                构建并运行所有测试"
    echo "  $0 --filter '*Adaptation*'  只运行Adaptation相关测试"
    echo "  $0 --build --verbose    详细模式构建测试"
}

# 函数：检查环境
check_environment() {
    print_info "检查测试环境..."
    
    # 检查是否在Android环境中
    if [ -z "$ANDROID_BUILD_TOP" ]; then
        print_error "未检测到Android构建环境，请先执行 source build/envsetup.sh 和 lunch"
        exit 1
    fi
    
    # 检查必要的工具
    local tools=("adb" "mm" "mmm")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            print_error "未找到必要工具: $tool"
            exit 1
        fi
    done
    
    # 检查设备连接
    if [ "$RUN_TESTS" = "true" ]; then
        if ! adb devices | grep -q "device$"; then
            print_error "未检测到连接的Android设备"
            exit 1
        fi
    fi
    
    print_success "环境检查通过"
}

# 函数：创建测试目录
create_test_directories() {
    print_info "创建测试目录..."
    
    adb shell "mkdir -p ${TEST_OUTPUT_DIR}" 2>/dev/null || true
    adb shell "mkdir -p /data/vendor/tms/test" 2>/dev/null || true
    
    print_success "测试目录创建完成"
}

# 函数：构建测试
build_tests() {
    print_info "构建测试..."
    
    cd "$PROJECT_ROOT"
    
    if [ "$VERBOSE" = "true" ]; then
        print_info "详细构建模式"
        mm -j$(nproc) tms_seimpl_tee_unit_tests
    else
        mm -j$(nproc) tms_seimpl_tee_unit_tests > /dev/null 2>&1
    fi
    
    if [ $? -eq 0 ]; then
        print_success "测试构建成功"
    else
        print_error "测试构建失败"
        exit 1
    fi
}

# 函数：推送测试文件到设备
push_test_files() {
    print_info "推送测试文件到设备..."
    
    # 推送测试二进制文件
    local test_binary_path="${ANDROID_PRODUCT_OUT}/vendor/bin/${TEST_BINARY}"
    if [ -f "$test_binary_path" ]; then
        adb push "$test_binary_path" "/vendor/bin/"
        adb shell "chmod 755 /vendor/bin/${TEST_BINARY}"
    else
        print_error "未找到测试二进制文件: $test_binary_path"
        exit 1
    fi
    
    # 推送测试配置文件（如果存在）
    local config_files=("${SCRIPT_DIR}/test_configs/"*.conf)
    for config_file in "${config_files[@]}"; do
        if [ -f "$config_file" ]; then
            adb push "$config_file" "/data/vendor/tms/test/"
        fi
    done
    
    print_success "测试文件推送完成"
}

# 函数：运行测试
run_tests() {
    print_info "运行测试..."
    
    create_test_directories
    push_test_files
    
    # 构建测试命令
    local test_cmd="/vendor/bin/${TEST_BINARY}"
    
    if [ -n "$TEST_FILTER" ]; then
        test_cmd="${test_cmd} --gtest_filter=${TEST_FILTER}"
    fi
    
    if [ "$GENERATE_XML" = "true" ]; then
        test_cmd="${test_cmd} --gtest_output=xml:${TEST_XML_FILE}"
    fi
    
    if [ "$VERBOSE" = "true" ]; then
        test_cmd="${test_cmd} --gtest_print_time=1"
    fi
    
    # 运行测试
    print_info "执行测试命令: $test_cmd"
    
    if [ "$VERBOSE" = "true" ]; then
        adb shell "$test_cmd" 2>&1 | tee "${TEST_LOG_FILE}"
    else
        adb shell "$test_cmd" > "${TEST_LOG_FILE}" 2>&1
    fi
    
    local test_result=$?
    
    # 拉取测试结果
    if [ "$GENERATE_XML" = "true" ]; then
        adb pull "${TEST_XML_FILE}" "${SCRIPT_DIR}/" 2>/dev/null || true
    fi
    
    adb pull "${TEST_LOG_FILE}" "${SCRIPT_DIR}/" 2>/dev/null || true
    
    # 分析测试结果
    if [ $test_result -eq 0 ]; then
        print_success "所有测试通过"
    else
        print_error "测试失败，请查看日志: ${SCRIPT_DIR}/test_log.txt"
        
        # 显示失败的测试
        if [ -f "${SCRIPT_DIR}/test_log.txt" ]; then
            print_info "失败的测试:"
            grep -E "\[  FAILED  \]" "${SCRIPT_DIR}/test_log.txt" || true
        fi
        
        exit 1
    fi
}

# 函数：清理构建产物
clean_build() {
    print_info "清理构建产物..."
    
    cd "$PROJECT_ROOT"
    mm clean-tms_seimpl_tee_unit_tests > /dev/null 2>&1 || true
    
    # 清理设备上的测试文件
    adb shell "rm -f /vendor/bin/${TEST_BINARY}" 2>/dev/null || true
    adb shell "rm -rf ${TEST_OUTPUT_DIR}" 2>/dev/null || true
    
    # 清理本地测试结果文件
    rm -f "${SCRIPT_DIR}/test_log.txt"
    rm -f "${SCRIPT_DIR}/test_results.xml"
    
    print_success "清理完成"
}

# 函数：生成代码覆盖率报告
generate_coverage() {
    print_info "生成代码覆盖率报告..."
    print_warning "代码覆盖率功能需要额外配置，当前版本暂不支持"
}

# 主函数
main() {
    local BUILD_TESTS=false
    local RUN_TESTS=false
    local CLEAN_BUILD=false
    local VERBOSE=false
    local GENERATE_XML=false
    local GENERATE_COVERAGE=false
    local TEST_FILTER=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--build)
                BUILD_TESTS=true
                shift
                ;;
            -r|--run)
                RUN_TESTS=true
                shift
                ;;
            -c|--clean)
                CLEAN_BUILD=true
                shift
                ;;
            -a|--all)
                BUILD_TESTS=true
                RUN_TESTS=true
                shift
                ;;
            -f|--filter)
                TEST_FILTER="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -x|--xml)
                GENERATE_XML=true
                shift
                ;;
            --coverage)
                GENERATE_COVERAGE=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定任何操作，显示帮助
    if [ "$BUILD_TESTS" = false ] && [ "$RUN_TESTS" = false ] && [ "$CLEAN_BUILD" = false ]; then
        show_help
        exit 0
    fi
    
    # 检查环境
    check_environment
    
    # 执行操作
    if [ "$CLEAN_BUILD" = true ]; then
        clean_build
    fi
    
    if [ "$BUILD_TESTS" = true ]; then
        build_tests
    fi
    
    if [ "$RUN_TESTS" = true ]; then
        run_tests
    fi
    
    if [ "$GENERATE_COVERAGE" = true ]; then
        generate_coverage
    fi
    
    print_success "操作完成"
}

# 执行主函数
main "$@"
