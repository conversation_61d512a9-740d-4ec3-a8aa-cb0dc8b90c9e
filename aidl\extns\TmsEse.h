/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#ifndef VENDOR_TMS_TMSESE_V1_0_TMSESE_H
#define VENDOR_TMS_TMSESE_V1_0_TMSESE_H

#include <stdint.h>
#include <aidl/vendor/tms/tmsese_aidl/BnTmsEse.h>

namespace aidl {
namespace vendor {
namespace tms {
namespace tmsese_aidl {

enum {
    TMS_SE_GENERIC_HARD_RESET = 0,    //eSE hard reset
    TMS_SE_GENERIC_UPDATE_COS,
    TMS_SE_GENERIC_UPDATE_PATCH,
    TMS_SE_GENERIC_UPDATE_APPLET,
    TMS_SE_GENERIC_TEST,             //SE TEST
};

struct TmsEse : public BnTmsEse {
    ::ndk::ScopedAStatus setMtkSpiClk(bool in_enable, bool *_aidl_return) override;
    ::ndk::ScopedAStatus generic(int32_t in_cmd,
                                 const std::vector<uint8_t> &in_data,
                                 ::aidl::vendor::tms::tmsese_aidl::TmsSeGenericRsp *_aidl_return) override;
};

}  // namespace tmsese_aidl
}  // namespace tms
}  // namespace vendor
}  // namespace aidl


#endif  // VENDOR_TMS_TMSNFC_V1_0_TMSNFC_H
