# Copyright (C) 2021-2022 Tsingteng MicroSystem
#
# All rights are reserved. Reproduction in whole or in part is
# prohibited without the written consent of the copyright owner.
#
# Tsingteng reserves the right to make changes without notice at any time.
#
# Tsingteng makes no warranty, expressed, implied or statutory, including but
# not limited to any implied warranty of merchantability or fitness for any
# particular purpose, or that the use will not infringe any third party patent,
# copyright or trademark. Tsingteng must not be liable for any loss or damage
# arising from its use.

LOCAL_PATH:=$(call my-dir)
include $(CLEAR_VARS)

LOCAL_MODULE := libtms_seimpl_tee
LOCAL_VENDOR_MODULE := true
LOCAL_MODULE_OWNER := tms

$(info MTK_TEE_GP_SUPPORT = $(MTK_TEE_GP_SUPPORT))
ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
GPTEE_PATH := vendor/optee/optee_client/libteec/include/
else
ifeq ($(strip $(MTK_TEE_GP_SUPPORT)), yes)
GPTEE_PATH := vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/ClientLib/include/GP/
else
SMSM_PATH := vendor/qcom/proprietary/securemsm
GPTEE_PATH := $(SMSM_PATH)/GPTEE/inc
endif
endif
LOCAL_SRC_FILES := \
    src/TmsSeAdaptation.cpp \
    src/TmsSeGpApi.cpp \
    src/TmsSePlatProp.cpp \
    src/TmsSeGetConfig.cpp \
    extns/src/TmsSeFeatures.cpp \
    extns/src/TmsSeUpdate.cpp

LOCAL_C_INCLUDES += \
    $(GPTEE_PATH) \
    $(TARGET_OUT_HEADERS)/common/inc \
    $(LOCAL_PATH)/inc \
    $(LOCAL_PATH)/extns/inc

LOCAL_SHARED_LIBRARIES += \
    libc \
    libcutils \
    liblog \
    libdl \
    libutils \
    libhardware \
    libhidlbase \
    libbase \
    android.hardware.secure_element@1.0 \
    libtms_log_record
    # libbinder_ndk \
    # android.hardware.secure_element-V1-ndk

ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
LOCAL_STATIC_LIBRARIES += libteec
endif

LOCAL_STATIC_LIBRARIES :=  \
    libtms_base \
    libtms_cosdl_hidl \
    libtms_cosdl_trad
    # libtms_cosdl_aidl

LOCAL_CFLAGS += \
    -Wno-error=date-time \
    -Wno-date-time

ifeq ($(strip $(TMS_OPTEE_SUPPORT)),true)
LOCAL_CFLAGS += -DENABLE_OPTEE_FUNC
LOCAL_SHARED_LIBRARIES += libteec
else
ifeq ($(strip $(MTK_TEE_GP_SUPPORT)), yes)
LOCAL_CFLAGS += -DMTK_TRUSTONIC_TEE
LOCAL_SHARED_LIBRARIES += \
    libMcClient
LOCAL_SRC_FILES += \
    src/TmsSePhControl.cpp
else
LOCAL_SHARED_LIBRARIES += libGPTEE_vendor
endif
endif
include $(BUILD_SHARED_LIBRARY)
