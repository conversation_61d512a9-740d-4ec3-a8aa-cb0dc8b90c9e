/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "TmsSeUpdate.h"
#include "TmsSeAdaptation.h"

#ifdef TAG
#undef TAG
#endif
#define TAG    "SeUpdate"

using namespace std;
using namespace tms::cosdl;

bool compareStrings(const string& a, const string& b) {
    return a.compare(b) < 0;
}

bool isMatchPrefixAndSuffix(const string& filename) {
    const string prefix = "SEMS_";
    const string suffix = ".txt";

    TMSLOG_D("%s: Check %s", __func__, filename.c_str());

    if (prefix.length() > filename.length() || suffix.length() > filename.length()){
        return false;
    }

    if (filename.compare(0, prefix.length(), prefix) != 0) {
        return false;
    }

    if (filename.compare(filename.length() - suffix.length(), suffix.length(), suffix) != 0) {
        return false;
    }

    return true;
}

DLStatus collectAppletFilenames(const string& path, vector<string>& appletList) {

    TMSLOG_D("%s: enter", __func__);
    DIR* dir = opendir(path.c_str());
    if (!dir) {
        TMSLOG_E("%s: Unable to open applets path", __func__);
        return DLStatus::FILE_OPEN_FAILED;
    }

    appletList.clear();
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        string filename(entry->d_name);

        if (entry->d_type == DT_REG && isMatchPrefixAndSuffix(filename)) {
            TMSLOG_D("%s: Get %s", __func__, filename.c_str());
            appletList.push_back(entry->d_name);
        }

    }

    closedir(dir);
    TMSLOG_I("%s: exit, the number of files is %u", __func__, (unsigned int)appletList.size());
    return DLStatus::OK;
}

DLStatus DlgotoBl(void)
{
    TMSSTATUS status;

#ifdef MTK_TRUSTONIC_TEE
    if (!hardReset()) {
        return DLStatus::GOTO_BL_ERROR;
    }

    status = TMS_SUCCESS;
#endif

    TEEC_Result result = tmsesesvc_generic(TMS_GENERIC_CMD_HARD_RESET, 0, nullptr);

    if (TEEC_SUCCESS == result) {
        tmsSeChannelsRecClean();
        status = TMS_SUCCESS;
    } else {
        TMSLOG_E("%s: tmsSeReset hard reset failed!!", __func__);
        status = TMS_FAILED;
    }

    TMSLOG_I("%s: status = %d", __func__, status);
    return status == TMS_SUCCESS ? DLStatus::OK : DLStatus::GOTO_BL_ERROR;
}

DLStatus DlGotoCos(void)
{
    /* Check and enter the COS */
    TMSSTATUS status = tmsSeStatusCheck(STATUS_CHECK);
    TMSLOG_I("%s: status = %d", __func__, status);
    return status == TMS_SUCCESS ? DLStatus::OK : DLStatus::GOTO_COS_ERROR;
}

static SEConfig gSEConfig = {
    .gotoBl = DlgotoBl,
    .gotoCos = DlGotoCos,
    .readerName = "eSE1",
};

DLStatus tmsSeCheckCosUpdate()
{
    DLStatus ret;
    char updatePath[TMS_UPDATE_PATH_NAME_LEN_MAX] = {0x00};
    DLConfig config = {
        .script_type = TEXT,
        .skip_version_check = true,
        .auto_open_channel = true,
        .upgrade_under_bl = true,
        .skip_unknown_coammnd = false,
        .is_debug_enable = true,
        .update_type = UpdateType::COS,
    };

    TMSLOG_D("%s: enter", __func__);
    ConfigGetString(updatePath, TMS_UPDATE_PATH_NAME_LEN_MAX,
                    NAME_TMS_ESE_UPDATE_COS, strlen(NAME_TMS_ESE_UPDATE_COS),
                    "/vendor/firmware/tms/T9_COS.txt", strlen("/vendor/firmware/tms/T9_COS.txt"));

    for (size_t i = 0; i < 2; i++) {
        ret = update(&config, &gSEConfig, updatePath);
        if (ret == DLStatus::OK || ret == DLStatus::FILE_NOT_EXIST || ret == DLStatus::SKIP_UPGRADE) {
            break;
        }
    }

    TMSLOG_I("%s: exit, ret = %d", __func__, ret);
    return ret;
}

DLStatus tmsSeCheckCosPatchUpdate()
{
    DLStatus ret;
    char updatePath[TMS_UPDATE_PATH_NAME_LEN_MAX] = {0x00};
    DLConfig config = {
        .script_type = TEXT,
        .skip_version_check = false,
        .auto_open_channel = true,
        .upgrade_under_bl = false,
        .skip_unknown_coammnd = false,
        .is_debug_enable = true,
        .update_type = UpdateType::PATCH,
    };

    TMSLOG_I("%s: enter", __func__);
    ConfigGetString(updatePath, TMS_UPDATE_PATH_NAME_LEN_MAX,
                    NAME_TMS_ESE_UPDATE_PATCH, strlen(NAME_TMS_ESE_UPDATE_PATCH),
                    "/vendor/firmware/tms/T9_COS_PATCH.txt", strlen("/vendor/firmware/tms/T9_COS_PATCH.txt"));

    for (size_t i = 0; i < 2; i++) {
        ret = update(&config, &gSEConfig, updatePath);
        if (ret == DLStatus::OK || ret == DLStatus::FILE_NOT_EXIST || ret == DLStatus::SKIP_UPGRADE) {
            break;
        }
    }

    TMSLOG_I("%s: exit, ret = %d", __func__, ret);
    return ret;
}

DLStatus tmsSeCheckAppletUpdate()
{
    TEE_SESessionHandle session = nullptr;
    DLStatus ret;
    vector<string> appletList;
    string path(TMS_UPDATE_PATH_NAME_LEN_MAX, '\0');
    DLConfig config = {
        .script_type = TEXT,
        .skip_version_check = true,
        .auto_open_channel = true,
        .upgrade_under_bl = false,
        .skip_unknown_coammnd = false,
        .is_debug_enable = true,
        .update_type = UpdateType::APPLET,
    };

    TMSLOG_I("%s: enter", __func__);
    ConfigGetString(path.data(), path.size(),
                    NAME_TMS_ESE_UPDATE_APPLET, strlen(NAME_TMS_ESE_UPDATE_APPLET),
                    "/vendor/firmware/tms/applets/", strlen("/vendor/firmware/tms/applets/"));

    ret = collectAppletFilenames(path, appletList);

    if (ret == DLStatus::OK && appletList.size() == 0) {
        ret = DLStatus::FILE_NOT_EXIST;
        goto exit;
    } else if (ret != DLStatus::OK) {
        goto exit;
    }

    sort(appletList.begin(), appletList.end(), compareStrings);

    if (!tmsSeUpdateOpenSession(&session)) {
        TMSLOG_E("%s: tmsSeUpdateOpenSession failed", __func__);
        ret = DLStatus::FAILED;
        goto exit;
    }

    for (const auto& filename : appletList) {
        for (size_t i = 0; i < 3; i++) {
            string appletPath = path + filename;
            ret = update(&config, &gSEConfig, appletPath.c_str());
            if (ret == DLStatus::OK || ret == DLStatus::SKIP_UPGRADE) {
                TMSLOG_I("%s: %s update %s", __func__, filename.c_str(), (ret == DLStatus::OK ? "success" : "skip"));
                break;
            }
        }

        if (ret != DLStatus::OK && ret != DLStatus::SKIP_UPGRADE) {
            TMSLOG_E("%s: %s is update failed, goto recovery...", __func__, filename.c_str());
            string recPath = path + "REC_" + filename;
            ret = update(&config, &gSEConfig, recPath.c_str());
            TMSLOG_E("%s: Recovery applet %s", __func__, (ret == DLStatus::OK ? "success" : "failed"));
        }

    }

    tmsSeUpdateCloseSession(session);
exit:
    TMSLOG_I("%s: exit, ret = %d", __func__, ret);
    return ret;
}
