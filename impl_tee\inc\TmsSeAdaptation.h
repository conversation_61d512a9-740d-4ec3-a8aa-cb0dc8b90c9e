/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#ifndef _TMS_SE_ADAPTATION_
#define _TMS_SE_ADAPTATION_

#include <memory>
#include "TmsSeGpApi.h"
#ifdef MTK_TRUSTONIC_TEE
#include "TmsSePhControl.h"
#endif
#include "TmsVersion.h"

#define TMS_MAJOR_VERSION 3
#define TMS_MINOR_VERSION 0
#define TMS_MAINTENANCE_VERSION 1

#ifndef MAX_LOGICAL_CHANNELS
#define MAX_LOGICAL_CHANNELS 0x04
#endif
#ifndef MIN_APDU_LENGTH
#define MIN_APDU_LENGTH 0x04
#endif
#ifndef DEFAULT_BASIC_CHANNEL
#define DEFAULT_BASIC_CHANNEL 0x00
#endif

class TmsSeInstance {
private:
    TmsSeInstance() = default;
    TmsSeInstance(const TmsSeInstance&) = delete;
    TmsSeInstance& operator=(const TmsSeInstance&) = delete;

    static std::string configName;

public:
    static TmsSeInstance& getInstance() {
        static TmsSeInstance instance;
        return instance;
    }

    void setConfigName(const std::string& newName) {
        configName = newName;
    }

    std::string getConfigName() const {
        return configName;
    }
};

#ifdef __cplusplus
extern "C" {
#endif

TMSSTATUS tmsSeInit();
bool tmsIsSeInitialized();
void tmsSeDeInit();
TMSSTATUS tmsSeGetAtr(std::vector<uint8_t> &atr);
bool tmsIsSeCardPresent();
TMSSTATUS tmsSeTransmit(std::vector<uint8_t> &cmdApdu,
                        std::vector<uint8_t> &rspApdu);
TMSSTATUS tmsOpenLogicalChannel(std::vector<uint8_t> &aid, uint8_t p2,
                                uint8_t *channelNum, std::vector<uint8_t> &selectRsp);
TMSSTATUS tmsOpenBasicChannel(std::vector<uint8_t> &aid, uint8_t p2,
                              std::vector<uint8_t> &selectRsp);
TMSSTATUS tmsSeCloseChannel(uint8_t channelNum);
TMSSTATUS tmsSeReset();
TMSSTATUS tmsSeHardReset();
TMSSTATUS tmsSeStatusCheck(SeCheckMode mode);
TMSSTATUS tmsSeTestTimer(uint32_t control, uint32_t *time);
void tmsSeChannelsRecClean();
bool tmsSeUpdateOpenSession(TEE_SESessionHandle *session);
void tmsSeUpdateCloseSession(TEE_SESessionHandle session);
#ifdef __cplusplus
}
#endif

#endif
