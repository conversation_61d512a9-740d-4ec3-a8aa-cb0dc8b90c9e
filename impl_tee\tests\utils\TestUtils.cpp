/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#include "TestUtils.h"
#include <algorithm>
#include <random>
#include <chrono>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <unistd.h>
#include <sys/stat.h>
#include <android/log.h>

bool TestUtils::s_testEnvironmentInitialized = false;
bool TestUtils::s_testLoggingEnabled = false;

void TestUtils::setupTestEnvironment() {
    if (s_testEnvironmentInitialized) {
        return;
    }
    
    // 启用测试日志
    enableTestLogging();
    
    // 创建测试配置目录
    std::string configPath = getTestConfigPath();
    mkdir(configPath.c_str(), 0755);
    
    logTestInfo("Test environment setup completed");
    s_testEnvironmentInitialized = true;
}

void TestUtils::cleanupTestEnvironment() {
    if (!s_testEnvironmentInitialized) {
        return;
    }
    
    // 清理测试配置文件
    std::string configPath = getTestConfigPath();
    // 这里可以添加清理逻辑
    
    logTestInfo("Test environment cleanup completed");
    s_testEnvironmentInitialized = false;
}

std::string TestUtils::vectorToHexString(const std::vector<uint8_t>& data) {
    std::stringstream ss;
    ss << std::hex << std::uppercase << std::setfill('0');
    for (size_t i = 0; i < data.size(); ++i) {
        if (i > 0) ss << " ";
        ss << std::setw(2) << static_cast<int>(data[i]);
    }
    return ss.str();
}

std::vector<uint8_t> TestUtils::hexStringToVector(const std::string& hexStr) {
    std::vector<uint8_t> result;
    std::string cleanStr = hexStr;
    
    // 移除空格
    cleanStr.erase(std::remove(cleanStr.begin(), cleanStr.end(), ' '), cleanStr.end());
    
    // 确保长度为偶数
    if (cleanStr.length() % 2 != 0) {
        return result; // 返回空向量表示错误
    }
    
    for (size_t i = 0; i < cleanStr.length(); i += 2) {
        std::string byteStr = cleanStr.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoul(byteStr, nullptr, 16));
        result.push_back(byte);
    }
    
    return result;
}

bool TestUtils::isVectorEqual(const std::vector<uint8_t>& v1, const std::vector<uint8_t>& v2) {
    if (v1.size() != v2.size()) {
        return false;
    }
    return std::equal(v1.begin(), v1.end(), v2.begin());
}

std::vector<uint8_t> TestUtils::createSelectApdu(const std::vector<uint8_t>& aid, uint8_t p2) {
    std::vector<uint8_t> apdu;
    apdu.push_back(0x00); // CLA
    apdu.push_back(0xA4); // INS
    apdu.push_back(0x04); // P1
    apdu.push_back(p2);   // P2
    apdu.push_back(static_cast<uint8_t>(aid.size())); // Lc
    apdu.insert(apdu.end(), aid.begin(), aid.end()); // Data
    return apdu;
}

std::vector<uint8_t> TestUtils::createGetResponseApdu(uint8_t le) {
    return {0x00, 0xC0, 0x00, 0x00, le};
}

std::vector<uint8_t> TestUtils::createManageChannelApdu(uint8_t instruction, uint8_t channelNum) {
    return {0x00, instruction, 0x00, channelNum, 0x01, 0x00};
}

bool TestUtils::isSuccessResponse(const std::vector<uint8_t>& response) {
    if (response.size() < 2) {
        return false;
    }
    uint16_t sw = getStatusWord(response);
    return sw == 0x9000;
}

uint16_t TestUtils::getStatusWord(const std::vector<uint8_t>& response) {
    if (response.size() < 2) {
        return 0x0000;
    }
    size_t len = response.size();
    return (static_cast<uint16_t>(response[len-2]) << 8) | response[len-1];
}

std::vector<uint8_t> TestUtils::generateRandomData(size_t length) {
    std::vector<uint8_t> data(length);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);
    
    for (size_t i = 0; i < length; ++i) {
        data[i] = static_cast<uint8_t>(dis(gen));
    }
    
    return data;
}

std::vector<uint8_t> TestUtils::generateTestAid(uint8_t aidLength) {
    std::vector<uint8_t> aid;
    for (uint8_t i = 0; i < aidLength; ++i) {
        aid.push_back(i + 1);
    }
    return aid;
}

std::vector<uint8_t> TestUtils::generateTestAtr() {
    return TmsSeTest::TEST_ATR;
}

bool TestUtils::createTestConfigFile(const std::string& filename, const std::string& content) {
    std::string fullPath = getTestConfigPath() + "/" + filename;
    std::ofstream file(fullPath);
    if (!file.is_open()) {
        return false;
    }
    file << content;
    file.close();
    return true;
}

bool TestUtils::deleteTestConfigFile(const std::string& filename) {
    std::string fullPath = getTestConfigPath() + "/" + filename;
    return unlink(fullPath.c_str()) == 0;
}

std::string TestUtils::getTestConfigPath() {
    return "/data/vendor/tms/test";
}

void TestUtils::sleepMs(uint32_t milliseconds) {
    usleep(milliseconds * 1000);
}

uint64_t TestUtils::getCurrentTimeMs() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
}

void TestUtils::enableTestLogging() {
    s_testLoggingEnabled = true;
}

void TestUtils::disableTestLogging() {
    s_testLoggingEnabled = false;
}

void TestUtils::logTestInfo(const std::string& message) {
    if (s_testLoggingEnabled) {
        __android_log_print(ANDROID_LOG_INFO, "TmsSeTest", "%s", message.c_str());
    }
}

void TestUtils::logTestError(const std::string& message) {
    if (s_testLoggingEnabled) {
        __android_log_print(ANDROID_LOG_ERROR, "TmsSeTest", "%s", message.c_str());
    }
}

void* TestUtils::allocateAlignedMemory(size_t size, size_t alignment) {
    void* ptr = nullptr;
    if (posix_memalign(&ptr, alignment, size) != 0) {
        return nullptr;
    }
    return ptr;
}

void TestUtils::freeAlignedMemory(void* ptr) {
    if (ptr) {
        free(ptr);
    }
}

bool TestUtils::isMtkPlatform() {
#ifdef MTK_TRUSTONIC_TEE
    return true;
#else
    return false;
#endif
}

bool TestUtils::isQcomPlatform() {
#ifdef MTK_TRUSTONIC_TEE
    return false;
#else
    return true; // 默认假设是QCOM平台
#endif
}

bool TestUtils::isOpteeSupported() {
#ifdef ENABLE_OPTEE_FUNC
    return true;
#else
    return false;
#endif
}
