/*
 * Copyright (c) 2023 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#ifndef _TMS_SE_PHCONTROL_H_
#define _TMS_SE_PHCONTROL_H_

#include <sys/ioctl.h>
#include <errno.h>
#include <stdlib.h>
#include "TmsSeGpApi.h"

#define ESE_MAGIC                (0xEA)
#define ESE_HARD_RESET           _IOW(ESE_MAGIC, 0x02, long)  /* MTK TEE set T9 SE hard reset */
#define ESE_SPI_CLK_CONTROL      _IOW(ESE_MAGIC, 0xFF, long)  /* MTK TEE set SE SPI clock on/off */
#define WAIT_TIME                (200 * 1000)  // 200ms
#define OPEN_RETRY_TIME          10
#define CONTROL_RETRY_TIME       3

#define SPI_CLK_ON               1
#define SPI_CLK_OFF              0

bool requestSpiClk();
bool releaseSpiClk();
bool releaseSpiClk(uint8_t num);
bool hardReset();

#endif