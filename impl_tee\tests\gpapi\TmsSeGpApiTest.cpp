/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "TestCommon.h"
#include "TestUtils.h"
#include "MockTeeContext.h"
#include "TmsSeGpApi.h"

using ::testing::_;
using ::testing::Return;
using ::testing::SetArgPointee;
using ::testing::DoAll;
using ::testing::InSequence;

/**
 * TmsSeGpApi模块的单元测试类
 */
class TmsSeGpApiTest : public MockTestFixture {
protected:
    void SetUp() override {
        MockTestFixture::SetUp();
        setupDefaultMockBehavior();
        LOG_TEST_INFO("TmsSeGpApiTest setup completed");
    }
    
    void TearDown() override {
        // 清理TEE会话
        tmsesesvc_close();
        MockTestFixture::TearDown();
        LOG_TEST_INFO("TmsSeGpApiTest teardown completed");
    }
    
private:
    void setupDefaultMockBehavior() {
        // 设置TEE Context的默认行为
        EXPECT_CALL(*MOCK_TEE_CONTEXT(), InitializeContext(_, _))
            .WillRepeatedly(Return(TEEC_SUCCESS));
        
        EXPECT_CALL(*MOCK_TEE_CONTEXT(), OpenSession(_, _, _, _, _, _, _))
            .WillRepeatedly(Return(TEEC_SUCCESS));
        
        EXPECT_CALL(*MOCK_TEE_CONTEXT(), InvokeCommand(_, _, _, _))
            .WillRepeatedly(Return(TEEC_SUCCESS));
        
        EXPECT_CALL(*MOCK_TEE_CONTEXT(), AllocateSharedMemory(_, _))
            .WillRepeatedly(Return(TEEC_SUCCESS));
    }
};

/**
 * 测试TMS ESE服务打开功能
 */
TEST_F(TmsSeGpApiTest, TestTmsesesvcOpen_Success) {
    TEEC_Result result = tmsesesvc_open(ESE_MODE_NORMAL);
    EXPECT_TEEC_SUCCESS(result);
}

TEST_F(TmsSeGpApiTest, TestTmsesesvcOpen_AlreadyOpened) {
    // 先打开一次
    EXPECT_TEEC_SUCCESS(tmsesesvc_open(ESE_MODE_NORMAL));
    
    // 再次打开应该成功（幂等操作）
    EXPECT_TEEC_SUCCESS(tmsesesvc_open(ESE_MODE_NORMAL));
}

TEST_F(TmsSeGpApiTest, TestTmsesesvcOpen_ContextInitFailure) {
    // 模拟Context初始化失败
    EXPECT_CALL(*MOCK_TEE_CONTEXT(), InitializeContext(_, _))
        .WillOnce(Return(TEEC_ERROR_COMMUNICATION));
    
    TEEC_Result result = tmsesesvc_open(ESE_MODE_NORMAL);
    EXPECT_NE(TEEC_SUCCESS, result);
}

TEST_F(TmsSeGpApiTest, TestTmsesesvcOpen_SessionOpenFailure) {
    // 模拟Session打开失败
    EXPECT_CALL(*MOCK_TEE_CONTEXT(), OpenSession(_, _, _, _, _, _, _))
        .WillOnce(Return(TEEC_ERROR_ITEM_NOT_FOUND));
    
    TEEC_Result result = tmsesesvc_open(ESE_MODE_NORMAL);
    EXPECT_NE(TEEC_SUCCESS, result);
}

/**
 * 测试TMS ESE服务关闭功能
 */
TEST_F(TmsSeGpApiTest, TestTmsesesvcClose_Success) {
    // 先打开服务
    EXPECT_TEEC_SUCCESS(tmsesesvc_open(ESE_MODE_NORMAL));
    
    // 然后关闭服务
    TEEC_Result result = tmsesesvc_close();
    EXPECT_TEEC_SUCCESS(result);
}

TEST_F(TmsSeGpApiTest, TestTmsesesvcClose_NotOpened) {
    // 测试未打开状态下的关闭操作
    TEEC_Result result = tmsesesvc_close();
    EXPECT_TEEC_SUCCESS(result); // 应该成功，因为是幂等操作
}

/**
 * 测试TEE SE Service API
 */
TEST_F(TmsSeGpApiTest, TestTEE_SEServiceOpen_Success) {
    TEE_SEServiceHandle seServiceHandle = nullptr;
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEServiceOpen(_))
        .WillOnce(Return(TEEC_SUCCESS));
    
    TEEC_Result result = TEE_SEServiceOpen(&seServiceHandle);
    EXPECT_TEEC_SUCCESS(result);
    EXPECT_NE(nullptr, seServiceHandle);
}

TEST_F(TmsSeGpApiTest, TestTEE_SEServiceClose_Success) {
    TEE_SEServiceHandle seServiceHandle = reinterpret_cast<TEE_SEServiceHandle>(0x12345678);
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEServiceClose(_))
        .WillOnce(Return());
    
    // 应该不会崩溃
    TEE_SEServiceClose(seServiceHandle);
}

TEST_F(TmsSeGpApiTest, TestTEE_SEServiceGetReaders_Success) {
    TEE_SEServiceHandle seServiceHandle = reinterpret_cast<TEE_SEServiceHandle>(0x12345678);
    TEE_SEReaderHandle readerHandles[MAX_READER_NUM];
    uint32_t readerCount = MAX_READER_NUM;
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEServiceGetReaders(_, _, _))
        .WillOnce(DoAll(
            SetArgPointee<2>(1), // 设置reader数量为1
            Return(TEEC_SUCCESS)
        ));
    
    TEEC_Result result = TEE_SEServiceGetReaders(seServiceHandle, readerHandles, &readerCount);
    EXPECT_TEEC_SUCCESS(result);
    EXPECT_EQ(1u, readerCount);
}

/**
 * 测试TEE SE Reader API
 */
TEST_F(TmsSeGpApiTest, TestTEE_SEReaderGetProperties_Success) {
    TEE_SEReaderHandle readerHandle = reinterpret_cast<TEE_SEReaderHandle>(0x12345678);
    TEE_SEReaderProperties properties;
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEReaderGetProperties(_, _))
        .WillOnce(Return());
    
    // 应该不会崩溃
    TEE_SEReaderGetProperties(readerHandle, &properties);
}

TEST_F(TmsSeGpApiTest, TestTEE_SEReaderGetName_Success) {
    TEE_SEReaderHandle readerHandle = reinterpret_cast<TEE_SEReaderHandle>(0x12345678);
    char readerName[256];
    uint32_t readerNameLen = sizeof(readerName);
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEReaderGetName(_, _, _))
        .WillOnce(DoAll(
            SetArgPointee<2>(10), // 设置名称长度
            Return(TEEC_SUCCESS)
        ));
    
    TEEC_Result result = TEE_SEReaderGetName(readerHandle, readerName, &readerNameLen);
    EXPECT_TEEC_SUCCESS(result);
    EXPECT_EQ(10u, readerNameLen);
}

TEST_F(TmsSeGpApiTest, TestTEE_SEReaderOpenSession_Success) {
    TEE_SEReaderHandle readerHandle = reinterpret_cast<TEE_SEReaderHandle>(0x12345678);
    TEE_SESessionHandle sessionHandle = nullptr;
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEReaderOpenSession(_, _))
        .WillOnce(Return(TEEC_SUCCESS));
    
    TEEC_Result result = TEE_SEReaderOpenSession(readerHandle, &sessionHandle);
    EXPECT_TEEC_SUCCESS(result);
}

/**
 * 测试TEE SE Session API
 */
TEST_F(TmsSeGpApiTest, TestTEE_SESessionGetATR_Success) {
    TEE_SESessionHandle sessionHandle = reinterpret_cast<TEE_SESessionHandle>(0x12345678);
    uint8_t atr[256];
    uint32_t atrLen = sizeof(atr);
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SESessionGetATR(_, _, _))
        .WillOnce(DoAll(
            SetArgPointee<2>(TmsSeTest::TEST_ATR.size()),
            Return(TEEC_SUCCESS)
        ));
    
    TEEC_Result result = TEE_SESessionGetATR(sessionHandle, atr, &atrLen);
    EXPECT_TEEC_SUCCESS(result);
    EXPECT_EQ(TmsSeTest::TEST_ATR.size(), atrLen);
}

TEST_F(TmsSeGpApiTest, TestTEE_SESessionIsClosed_Success) {
    TEE_SESessionHandle sessionHandle = reinterpret_cast<TEE_SESessionHandle>(0x12345678);
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SESessionIsClosed(_))
        .WillOnce(Return(TEEC_SUCCESS));
    
    TEEC_Result result = TEE_SESessionIsClosed(sessionHandle);
    EXPECT_TEEC_SUCCESS(result);
}

TEST_F(TmsSeGpApiTest, TestTMSTEE_SESessionOpenBasicChannel_Success) {
    TEE_SESessionHandle sessionHandle = reinterpret_cast<TEE_SESessionHandle>(0x12345678);
    TEE_SEAID seAid;
    seAid.buffer = const_cast<uint8_t*>(TmsSeTest::TEST_AID.data());
    seAid.bufferLen = TmsSeTest::TEST_AID.size();
    TEE_SEChannelHandle channelHandle = nullptr;
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SESessionOpenBasicChannel(_, _, _, _))
        .WillOnce(Return(TEEC_SUCCESS));
    
    TEEC_Result result = TMSTEE_SESessionOpenBasicChannel(sessionHandle, &seAid, &channelHandle, TmsSeTest::TEST_P2_PARAM);
    EXPECT_TEEC_SUCCESS(result);
}

TEST_F(TmsSeGpApiTest, TestTMSTEE_SESessionOpenLogicalChannel_Success) {
    TEE_SESessionHandle sessionHandle = reinterpret_cast<TEE_SESessionHandle>(0x12345678);
    TEE_SEAID seAid;
    seAid.buffer = const_cast<uint8_t*>(TmsSeTest::TEST_AID.data());
    seAid.bufferLen = TmsSeTest::TEST_AID.size();
    TEE_SEChannelHandle channelHandle = nullptr;
    uint8_t channelNum = 0;
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SESessionOpenLogicalChannel(_, _, _, _, _))
        .WillOnce(DoAll(
            SetArgPointee<4>(TmsSeTest::TEST_LOGICAL_CHANNEL),
            Return(TEEC_SUCCESS)
        ));
    
    TEEC_Result result = TMSTEE_SESessionOpenLogicalChannel(sessionHandle, &seAid, &channelHandle, TmsSeTest::TEST_P2_PARAM, &channelNum);
    EXPECT_TEEC_SUCCESS(result);
    EXPECT_EQ(TmsSeTest::TEST_LOGICAL_CHANNEL, channelNum);
}

/**
 * 测试TEE SE Channel API
 */
TEST_F(TmsSeGpApiTest, TestTEE_SEChannelSelectNext_Success) {
    TEE_SEChannelHandle channelHandle = reinterpret_cast<TEE_SEChannelHandle>(0x12345678);
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEChannelSelectNext(_))
        .WillOnce(Return(TEEC_SUCCESS));
    
    TEEC_Result result = TEE_SEChannelSelectNext(channelHandle);
    EXPECT_TEEC_SUCCESS(result);
}

TEST_F(TmsSeGpApiTest, TestTEE_SEChannelGetSelectResponse_Success) {
    TEE_SEChannelHandle channelHandle = reinterpret_cast<TEE_SEChannelHandle>(0x12345678);
    uint8_t response[256];
    uint32_t responseLen = sizeof(response);
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEChannelGetSelectResponse(_, _, _))
        .WillOnce(DoAll(
            SetArgPointee<2>(TmsSeTest::TEST_SUCCESS_RESPONSE.size()),
            Return(TEEC_SUCCESS)
        ));
    
    TEEC_Result result = TEE_SEChannelGetSelectResponse(channelHandle, response, &responseLen);
    EXPECT_TEEC_SUCCESS(result);
    EXPECT_EQ(TmsSeTest::TEST_SUCCESS_RESPONSE.size(), responseLen);
}

TEST_F(TmsSeGpApiTest, TestTEE_SEChannelTransmit_Success) {
    TEE_SEChannelHandle channelHandle = reinterpret_cast<TEE_SEChannelHandle>(0x12345678);
    std::vector<uint8_t> command = TmsSeTest::TEST_SELECT_APDU;
    std::vector<uint8_t> response;
    
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEChannelTransmit(_, _, _))
        .WillOnce(DoAll(
            SetArgPointee<2>(TmsSeTest::TEST_SUCCESS_RESPONSE),
            Return(TEEC_SUCCESS)
        ));
    
    TEEC_Result result = TEE_SEChannelTransmit(channelHandle, command.data(), command.size(), nullptr, nullptr);
    EXPECT_TEEC_SUCCESS(result);
}

/**
 * 测试通用函数
 */
TEST_F(TmsSeGpApiTest, TestTmsesesvcGeneric_Success) {
    // 先打开服务
    EXPECT_TEEC_SUCCESS(tmsesesvc_open(ESE_MODE_NORMAL));
    
    TmsCapsule capsule;
    memset(&capsule, 0, sizeof(capsule));
    
    TEEC_Result result = tmsesesvc_generic(0x1000, 0, &capsule);
    EXPECT_TEEC_SUCCESS(result);
}

TEST_F(TmsSeGpApiTest, TestTmsesesvcGeneric_ServiceNotOpened) {
    TmsCapsule capsule;
    memset(&capsule, 0, sizeof(capsule));
    
    TEEC_Result result = tmsesesvc_generic(0x1000, 0, &capsule);
    EXPECT_NE(TEEC_SUCCESS, result);
}

/**
 * 测试内存拷贝函数
 */
TEST_F(TmsSeGpApiTest, TestMemscpy_Success) {
    uint8_t src[] = {0x01, 0x02, 0x03, 0x04};
    uint8_t dst[10];
    
    size_t copied = memscpy(dst, sizeof(dst), src, sizeof(src));
    EXPECT_EQ(sizeof(src), copied);
    EXPECT_EQ(0, memcmp(src, dst, sizeof(src)));
}

TEST_F(TmsSeGpApiTest, TestMemscpy_DstTooSmall) {
    uint8_t src[] = {0x01, 0x02, 0x03, 0x04};
    uint8_t dst[2];
    
    size_t copied = memscpy(dst, sizeof(dst), src, sizeof(src));
    EXPECT_EQ(sizeof(dst), copied);
    EXPECT_EQ(0, memcmp(src, dst, sizeof(dst)));
}
