/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "TmsSePlatProp.h"

#ifdef TAG
#undef TAG
#endif
#define TAG    "SePlatProp"

PlatProp getQcomPropFromConfig() {
    PlatProp prop;
    // spi config
    prop.qcom.clkPolarity = ConfigGetUnsigned(NAME_QTEE_ESE_PROP_SPI_CLK_POLARITY,
                                              strlen(NAME_QTEE_ESE_PROP_SPI_CLK_POLARITY), 0);
    prop.qcom.clkPhase = ConfigGetUnsigned(NAME_QTEE_ESE_PROP_SPI_SHIFT_MODE,
                                           strlen(NAME_QTEE_ESE_PROP_SPI_SHIFT_MODE), 0);
    prop.qcom.csPolarity = ConfigGetUnsigned(NAME_QTEE_ESE_PROP_SPI_CS_POLARITY,
                                             strlen(NAME_QTEE_ESE_PROP_SPI_CS_POLARITY), 0);
    prop.qcom.csMode = ConfigGetUnsigned(NAME_QTEE_ESE_PROP_SPI_CS_MODE,
                                         strlen(NAME_QTEE_ESE_PROP_SPI_CS_MODE), 1);
    prop.qcom.clkAlwaysOn = ConfigGetUnsigned(NAME_QTEE_ESE_PROP_SPI_CLK_ALWAYS_ON,
                                              strlen(NAME_QTEE_ESE_PROP_SPI_CLK_ALWAYS_ON), 0);
    prop.qcom.bitsPerWord = ConfigGetUnsigned(NAME_QTEE_ESE_PROP_SPI_BITS_PER_WORD,
                                              strlen(NAME_QTEE_ESE_PROP_SPI_BITS_PER_WORD), 8);
    return prop;
}

PlatProp getMtkPropFromConfig() {
    PlatProp prop;
    // spi config
    prop.mtk.setuptime = ConfigGetUnsigned(NAME_MTK_ESE_PROP_SPI_SETUP_TIME,
                                           strlen(NAME_MTK_ESE_PROP_SPI_SETUP_TIME), 2184);
    prop.mtk.holdtime = ConfigGetUnsigned(NAME_MTK_ESE_PROP_SPI_HOLD_TIME,
                                          strlen(NAME_MTK_ESE_PROP_SPI_HOLD_TIME), 110);
    prop.mtk.high_time = ConfigGetUnsigned(NAME_MTK_ESE_PROP_HIGH_TIME,
                                           strlen(NAME_MTK_ESE_PROP_HIGH_TIME), 10);
    prop.mtk.low_time = ConfigGetUnsigned(NAME_MTK_ESE_PROP_LOW_TIME,
                                          strlen(NAME_MTK_ESE_PROP_LOW_TIME), 10);
    prop.mtk.cs_idletime = ConfigGetUnsigned(NAME_MTK_ESE_PROP_CS_IDLE_TIME,
                                             strlen(NAME_MTK_ESE_PROP_CS_IDLE_TIME), 110);
    prop.mtk.ulthgh_thrsh = ConfigGetUnsigned(NAME_MTK_ESE_PROP_ULTHGH_THRSH,
                                              strlen(NAME_MTK_ESE_PROP_ULTHGH_THRSH), 0);
    prop.mtk.cpol = ConfigGetUnsigned(NAME_MTK_ESE_PROP_CPOL,
                                      strlen(NAME_MTK_ESE_PROP_CPOL), 0);
    prop.mtk.cpha = ConfigGetUnsigned(NAME_MTK_ESE_PROP_CPHA,
                                      strlen(NAME_MTK_ESE_PROP_CPHA), 0);
    prop.mtk.tx_mlsb = ConfigGetUnsigned(NAME_MTK_ESE_PROP_TX_MLSB,
                                         strlen(NAME_MTK_ESE_PROP_TX_MLSB), 1);
    prop.mtk.rx_mlsb = ConfigGetUnsigned(NAME_MTK_ESE_PROP_RX_MLSB,
                                         strlen(NAME_MTK_ESE_PROP_RX_MLSB), 1);
    prop.mtk.tx_endian = ConfigGetUnsigned(NAME_MTK_ESE_PROP_TX_ENDIAN,
                                           strlen(NAME_MTK_ESE_PROP_TX_ENDIAN), 0);
    prop.mtk.rx_endian = ConfigGetUnsigned(NAME_MTK_ESE_PROP_RX_ENDIAN,
                                           strlen(NAME_MTK_ESE_PROP_RX_ENDIAN), 0);
    prop.mtk.com_mod = ConfigGetUnsigned(NAME_MTK_ESE_PROP_COM_MOD,
                                         strlen(NAME_MTK_ESE_PROP_COM_MOD), 1);
    prop.mtk.pause = ConfigGetUnsigned(NAME_MTK_ESE_PROP_PAUSE,
                                       strlen(NAME_MTK_ESE_PROP_PAUSE), 0);
    prop.mtk.finish_intr = ConfigGetUnsigned(NAME_MTK_ESE_PROP_FINISH_INTR,
                                             strlen(NAME_MTK_ESE_PROP_FINISH_INTR), 0);
    prop.mtk.deassert = ConfigGetUnsigned(NAME_MTK_ESE_PROP_DEASSERT,
                                          strlen(NAME_MTK_ESE_PROP_DEASSERT), 0);
    prop.mtk.ulthigh = ConfigGetUnsigned(NAME_MTK_ESE_PROP_ULTHIGH,
                                         strlen(NAME_MTK_ESE_PROP_ULTHIGH), 0);
    prop.mtk.tckdly = ConfigGetUnsigned(NAME_MTK_ESE_PROP_TCKDLY,
                                        strlen(NAME_MTK_ESE_PROP_TCKDLY), 0);
    return prop;
}