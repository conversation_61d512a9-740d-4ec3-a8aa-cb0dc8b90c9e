/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "TestCommon.h"
#include "TestUtils.h"
#include "MockTeeContext.h"
#include "TmsSeAdaptation.h"

using ::testing::_;
using ::testing::Return;
using ::testing::SetArgPointee;
using ::testing::DoAll;
using ::testing::InSequence;

/**
 * TmsSeAdaptation模块的单元测试类
 */
class TmsSeAdaptationTest : public MockTestFixture {
protected:
    void SetUp() override {
        MockTestFixture::SetUp();
        
        // 设置默认的Mock行为
        setupDefaultMockBehavior();
        
        LOG_TEST_INFO("TmsSeAdaptationTest setup completed");
    }
    
    void TearDown() override {
        // 清理测试状态
        if (tmsIsSeInitialized()) {
            tmsSeDeInit();
        }
        
        MockTestFixture::TearDown();
        LOG_TEST_INFO("TmsSeAdaptationTest teardown completed");
    }
    
private:
    void setupDefaultMockBehavior() {
        // 设置TEE Context的默认行为
        EXPECT_CALL(*MOCK_TEE_CONTEXT(), InitializeContext(_, _))
            .WillRepeatedly(Return(TEEC_SUCCESS));
        
        EXPECT_CALL(*MOCK_TEE_CONTEXT(), OpenSession(_, _, _, _, _, _, _))
            .WillRepeatedly(Return(TEEC_SUCCESS));
        
        EXPECT_CALL(*MOCK_TEE_CONTEXT(), InvokeCommand(_, _, _, _))
            .WillRepeatedly(Return(TEEC_SUCCESS));
        
        // 设置SE API的默认行为
        EXPECT_CALL(*MOCK_TEE_SE_API(), SEServiceOpen(_))
            .WillRepeatedly(Return(TEEC_SUCCESS));
        
        EXPECT_CALL(*MOCK_TEE_SE_API(), SEServiceGetReaders(_, _, _))
            .WillRepeatedly(DoAll(
                SetArgPointee<2>(1), // 设置reader数量为1
                Return(TEEC_SUCCESS)
            ));
        
        EXPECT_CALL(*MOCK_TEE_SE_API(), SEReaderOpenSession(_, _))
            .WillRepeatedly(Return(TEEC_SUCCESS));
        
        EXPECT_CALL(*MOCK_TEE_SE_API(), SESessionGetATR(_, _, _))
            .WillRepeatedly(DoAll(
                SetArgPointee<2>(TmsSeTest::TEST_ATR.size()),
                Return(TEEC_SUCCESS)
            ));
    }
};

/**
 * 测试SE初始化功能
 */
TEST_F(TmsSeAdaptationTest, TestSeInit_Success) {
    // 测试正常初始化流程
    TMSSTATUS status = tmsSeInit();
    EXPECT_TMSSTATUS_SUCCESS(status);
    EXPECT_TRUE(tmsIsSeInitialized());
}

TEST_F(TmsSeAdaptationTest, TestSeInit_AlreadyInitialized) {
    // 测试重复初始化
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    EXPECT_TRUE(tmsIsSeInitialized());
    
    // 再次初始化应该成功（幂等操作）
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    EXPECT_TRUE(tmsIsSeInitialized());
}

TEST_F(TmsSeAdaptationTest, TestSeInit_TeeContextFailure) {
    // 模拟TEE Context初始化失败
    EXPECT_CALL(*MOCK_TEE_CONTEXT(), InitializeContext(_, _))
        .WillOnce(Return(TEEC_ERROR_COMMUNICATION));
    
    TMSSTATUS status = tmsSeInit();
    EXPECT_NE(TMSSTATUS_SUCCESS, status);
    EXPECT_FALSE(tmsIsSeInitialized());
}

/**
 * 测试SE去初始化功能
 */
TEST_F(TmsSeAdaptationTest, TestSeDeInit_Success) {
    // 先初始化
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    EXPECT_TRUE(tmsIsSeInitialized());
    
    // 然后去初始化
    tmsSeDeInit();
    EXPECT_FALSE(tmsIsSeInitialized());
}

TEST_F(TmsSeAdaptationTest, TestSeDeInit_NotInitialized) {
    // 测试未初始化状态下的去初始化
    EXPECT_FALSE(tmsIsSeInitialized());
    tmsSeDeInit(); // 应该不会崩溃
    EXPECT_FALSE(tmsIsSeInitialized());
}

/**
 * 测试获取ATR功能
 */
TEST_F(TmsSeAdaptationTest, TestGetAtr_Success) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    std::vector<uint8_t> atr;
    TMSSTATUS status = tmsSeGetAtr(atr);
    
    EXPECT_TMSSTATUS_SUCCESS(status);
    EXPECT_FALSE(atr.empty());
    EXPECT_VECTOR_EQ(TmsSeTest::TEST_ATR, atr);
}

TEST_F(TmsSeAdaptationTest, TestGetAtr_NotInitialized) {
    // 测试未初始化状态下获取ATR
    std::vector<uint8_t> atr;
    TMSSTATUS status = tmsSeGetAtr(atr);
    
    EXPECT_NE(TMSSTATUS_SUCCESS, status);
    EXPECT_TRUE(atr.empty());
}

/**
 * 测试卡片存在检测功能
 */
TEST_F(TmsSeAdaptationTest, TestIsCardPresent_Success) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    // 默认情况下卡片应该存在
    EXPECT_TRUE(tmsIsSeCardPresent());
}

TEST_F(TmsSeAdaptationTest, TestIsCardPresent_NotInitialized) {
    // 测试未初始化状态下的卡片检测
    EXPECT_FALSE(tmsIsSeCardPresent());
}

/**
 * 测试APDU传输功能
 */
TEST_F(TmsSeAdaptationTest, TestTransmit_Success) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    // 设置Mock期望
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEChannelTransmit(_, _, _))
        .WillOnce(DoAll(
            SetArgPointee<2>(TmsSeTest::TEST_SUCCESS_RESPONSE),
            Return(TEEC_SUCCESS)
        ));
    
    std::vector<uint8_t> cmdApdu = TmsSeTest::TEST_SELECT_APDU;
    std::vector<uint8_t> rspApdu;
    
    TMSSTATUS status = tmsSeTransmit(cmdApdu, rspApdu);
    
    EXPECT_TMSSTATUS_SUCCESS(status);
    EXPECT_VECTOR_EQ(TmsSeTest::TEST_SUCCESS_RESPONSE, rspApdu);
}

TEST_F(TmsSeAdaptationTest, TestTransmit_InvalidApdu) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    // 测试无效的APDU（长度太短）
    std::vector<uint8_t> cmdApdu = {0x00, 0xA4}; // 长度不足
    std::vector<uint8_t> rspApdu;
    
    TMSSTATUS status = tmsSeTransmit(cmdApdu, rspApdu);
    EXPECT_NE(TMSSTATUS_SUCCESS, status);
}

/**
 * 测试逻辑通道操作
 */
TEST_F(TmsSeAdaptationTest, TestOpenLogicalChannel_Success) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    // 设置Mock期望
    EXPECT_CALL(*MOCK_TEE_SE_API(), SESessionOpenLogicalChannel(_, _, _, _, _))
        .WillOnce(DoAll(
            SetArgPointee<4>(TmsSeTest::TEST_LOGICAL_CHANNEL),
            Return(TEEC_SUCCESS)
        ));
    
    std::vector<uint8_t> aid = TmsSeTest::TEST_AID;
    uint8_t channelNum = 0;
    std::vector<uint8_t> selectRsp;
    
    TMSSTATUS status = tmsOpenLogicalChannel(aid, TmsSeTest::TEST_P2_PARAM, &channelNum, selectRsp);
    
    EXPECT_TMSSTATUS_SUCCESS(status);
    EXPECT_EQ(TmsSeTest::TEST_LOGICAL_CHANNEL, channelNum);
}

TEST_F(TmsSeAdaptationTest, TestOpenLogicalChannel_NoAvailableChannel) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    // 模拟没有可用通道的情况
    EXPECT_CALL(*MOCK_TEE_SE_API(), SESessionOpenLogicalChannel(_, _, _, _, _))
        .WillOnce(Return(TEEC_ERROR_ITEM_NOT_FOUND));
    
    std::vector<uint8_t> aid = TmsSeTest::TEST_AID;
    uint8_t channelNum = 0;
    std::vector<uint8_t> selectRsp;
    
    TMSSTATUS status = tmsOpenLogicalChannel(aid, TmsSeTest::TEST_P2_PARAM, &channelNum, selectRsp);
    EXPECT_NE(TMSSTATUS_SUCCESS, status);
}

/**
 * 测试基本通道操作
 */
TEST_F(TmsSeAdaptationTest, TestOpenBasicChannel_Success) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    // 设置Mock期望
    EXPECT_CALL(*MOCK_TEE_SE_API(), SESessionOpenBasicChannel(_, _, _, _))
        .WillOnce(Return(TEEC_SUCCESS));
    
    std::vector<uint8_t> aid = TmsSeTest::TEST_AID;
    std::vector<uint8_t> selectRsp;
    
    TMSSTATUS status = tmsOpenBasicChannel(aid, TmsSeTest::TEST_P2_PARAM, selectRsp);
    EXPECT_TMSSTATUS_SUCCESS(status);
}

/**
 * 测试通道关闭功能
 */
TEST_F(TmsSeAdaptationTest, TestCloseChannel_Success) {
    // 先初始化SE并打开逻辑通道
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    // 设置Mock期望
    EXPECT_CALL(*MOCK_TEE_SE_API(), SEChannelClose(_))
        .WillOnce(Return());
    
    TMSSTATUS status = tmsSeCloseChannel(TmsSeTest::TEST_LOGICAL_CHANNEL);
    EXPECT_TMSSTATUS_SUCCESS(status);
}

TEST_F(TmsSeAdaptationTest, TestCloseChannel_InvalidChannel) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    // 测试关闭无效通道
    TMSSTATUS status = tmsSeCloseChannel(0xFF); // 无效通道号
    EXPECT_NE(TMSSTATUS_SUCCESS, status);
}

/**
 * 测试SE重置功能
 */
TEST_F(TmsSeAdaptationTest, TestSeReset_Success) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    TMSSTATUS status = tmsSeReset();
    EXPECT_TMSSTATUS_SUCCESS(status);
}

/**
 * 测试状态检查功能
 */
TEST_F(TmsSeAdaptationTest, TestStatusCheck_Success) {
    // 先初始化SE
    EXPECT_TMSSTATUS_SUCCESS(tmsSeInit());
    
    TMSSTATUS status = tmsSeStatusCheck(STATUS_CHECK);
    EXPECT_TMSSTATUS_SUCCESS(status);
}

TEST_F(TmsSeAdaptationTest, TestStatusCheck_BaseCheck) {
    // 测试基础检查模式
    TMSSTATUS status = tmsSeStatusCheck(BASE_CHECK);
    EXPECT_TMSSTATUS_SUCCESS(status);
}
