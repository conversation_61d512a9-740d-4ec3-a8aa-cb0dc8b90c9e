# TMS SE impl_tee 单元测试框架

## 概述

这是一个为 TMS SE impl_tee 模块设计的模块化单元测试框架。该框架基于 Google Test 和 Google Mock，提供了完整的测试覆盖和易于扩展的架构。

## 特性

- **模块化设计**: 每个模块都有独立的测试类，便于维护和扩展
- **Mock支持**: 使用Google Mock模拟TEE API和配置管理
- **参数化测试**: 支持参数化测试，提高测试覆盖率
- **测试工具**: 提供丰富的测试工具类和辅助函数
- **自动化脚本**: 包含完整的构建和运行脚本
- **详细日志**: 支持详细的测试日志和XML报告

## 目录结构

```
impl_tee/tests/
├── Android.bp                 # Android构建配置
├── test_main.cpp             # 测试主入口
├── run_tests.sh              # 测试运行脚本
├── README.md                 # 本文档
├── include/
│   └── TestCommon.h          # 通用测试定义和宏
├── utils/
│   ├── TestUtils.h           # 测试工具类头文件
│   └── TestUtils.cpp         # 测试工具类实现
├── mocks/
│   ├── MockTeeContext.h      # TEE Context Mock类
│   └── MockTeeContext.cpp    # TEE Context Mock实现
├── adaptation/
│   └── TmsSeAdaptationTest.cpp   # 适配层测试
├── gpapi/
│   └── TmsSeGpApiTest.cpp        # GP API测试
├── config/
│   └── TmsSeGetConfigTest.cpp    # 配置管理测试
└── platprop/
    └── TmsSePlatPropTest.cpp     # 平台属性测试
```

## 快速开始

### 1. 环境准备

确保您的Android构建环境已正确设置：

```bash
source build/envsetup.sh
lunch <your_target>
```

### 2. 构建测试

```bash
# 进入测试目录
cd impl_tee/tests

# 构建测试
./run_tests.sh --build
```

### 3. 运行测试

```bash
# 运行所有测试
./run_tests.sh --run

# 或者构建并运行
./run_tests.sh --all
```

### 4. 运行特定测试

```bash
# 只运行适配层测试
./run_tests.sh --run --filter "*Adaptation*"

# 只运行配置相关测试
./run_tests.sh --run --filter "*Config*"
```

## 测试模块说明

### 1. TmsSeAdaptationTest

测试 `TmsSeAdaptation.cpp` 模块的功能，包括：
- SE初始化和去初始化
- ATR获取
- 卡片存在检测
- APDU传输
- 通道管理（基本通道和逻辑通道）
- SE重置和状态检查

### 2. TmsSeGpApiTest

测试 `TmsSeGpApi.cpp` 模块的功能，包括：
- TMS ESE服务管理
- TEE SE Service API
- TEE SE Reader API
- TEE SE Session API
- TEE SE Channel API
- 通用函数和内存操作

### 3. TmsSeGetConfigTest

测试 `TmsSeGetConfig.cpp` 模块的功能，包括：
- 配置键值对读取
- 字符串和数值配置获取
- 默认值处理
- 配置清理功能
- C接口函数测试

### 4. TmsSePlatPropTest

测试 `TmsSePlatProp.cpp` 模块的功能，包括：
- QCOM平台属性获取
- MTK平台属性获取
- 默认值和边界值测试
- 参数化测试不同配置组合

## Mock框架

### MockTeeContext

模拟TEE客户端API的行为，包括：
- Context初始化和清理
- Session管理
- Command调用
- SharedMemory操作

### MockTeeSeApi

模拟TEE SE API的行为，包括：
- SE Service操作
- SE Reader操作
- SE Session操作
- SE Channel操作

### MockConfigManager

模拟配置管理的行为，包括：
- 配置键检查
- 字符串和数值配置获取
- 字节数组配置获取

## 测试工具

### TestUtils类

提供丰富的测试辅助功能：
- 数据转换工具（十六进制字符串转换）
- APDU构造工具
- 测试数据生成
- 文件操作工具
- 时间和日志工具
- 内存管理工具
- 平台检测工具

### 测试宏

提供便利的测试断言宏：
- `ASSERT_TMSSTATUS_SUCCESS` / `EXPECT_TMSSTATUS_SUCCESS`
- `ASSERT_TEEC_SUCCESS` / `EXPECT_TEEC_SUCCESS`
- `ASSERT_VECTOR_EQ` / `EXPECT_VECTOR_EQ`

## 扩展测试

### 添加新的测试模块

1. 在相应目录下创建测试文件（如 `NewModuleTest.cpp`）
2. 继承适当的测试基类
3. 实现测试用例
4. 更新 `Android.bp` 中的源文件列表

示例：

```cpp
#include "TestCommon.h"
#include "MockTeeContext.h"
#include "NewModule.h"

class NewModuleTest : public MockTestFixture {
protected:
    void SetUp() override {
        MockTestFixture::SetUp();
        // 设置特定的Mock行为
    }
};

TEST_F(NewModuleTest, TestNewFunction) {
    // 测试实现
    EXPECT_TMSSTATUS_SUCCESS(newFunction());
}
```

### 添加新的Mock类

1. 在 `mocks/` 目录下创建Mock头文件和实现文件
2. 继承相应的接口或创建虚拟接口
3. 使用 `MOCK_METHOD` 宏定义Mock方法
4. 在测试中设置期望行为

## 运行选项

### 基本选项

- `-h, --help`: 显示帮助信息
- `-b, --build`: 构建测试
- `-r, --run`: 运行测试
- `-c, --clean`: 清理构建产物
- `-a, --all`: 构建并运行测试

### 高级选项

- `-f, --filter PATTERN`: 运行匹配模式的测试
- `-v, --verbose`: 详细输出
- `-x, --xml`: 生成XML格式的测试报告
- `--coverage`: 生成代码覆盖率报告（待实现）

## 故障排除

### 常见问题

1. **构建失败**
   - 检查Android构建环境是否正确设置
   - 确保所有依赖库都已构建
   - 检查编译错误信息

2. **测试运行失败**
   - 确保设备已连接并可通过adb访问
   - 检查设备上的权限设置
   - 查看测试日志文件

3. **Mock行为不符合预期**
   - 检查Mock设置是否正确
   - 确保Mock期望与实际调用匹配
   - 使用 `::testing::StrictMock` 进行严格检查

### 调试技巧

1. 使用 `--verbose` 选项获取详细输出
2. 使用 `--filter` 选项运行特定测试
3. 检查生成的XML报告获取详细结果
4. 在测试代码中添加 `LOG_TEST_INFO` 进行调试

## 贡献指南

1. 遵循现有的代码风格和命名约定
2. 为新功能添加相应的测试用例
3. 确保所有测试都能通过
4. 更新相关文档

## 许可证

Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
