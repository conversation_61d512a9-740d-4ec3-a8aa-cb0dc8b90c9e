/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#ifndef TMS_SE_TEST_COMMON_H
#define TMS_SE_TEST_COMMON_H

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <vector>
#include <memory>
#include <string>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <functional>

// 测试用的常量定义
namespace TmsSeTest {

// 测试用的APDU命令
static const std::vector<uint8_t> TEST_SELECT_APDU = {
    0x00, 0xA4, 0x04, 0x00, 0x08, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08
};

static const std::vector<uint8_t> TEST_GET_RESPONSE_APDU = {
    0x00, 0xC0, 0x00, 0x00, 0x00
};

// 测试用的响应数据
static const std::vector<uint8_t> TEST_SUCCESS_RESPONSE = {0x90, 0x00};
static const std::vector<uint8_t> TEST_ERROR_RESPONSE = {0x6A, 0x82};

// 测试用的AID
static const std::vector<uint8_t> TEST_AID = {
    0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08
};

// 测试用的ATR
static const std::vector<uint8_t> TEST_ATR = {
    0x3B, 0x8F, 0x80, 0x01, 0x80, 0x4F, 0x0C, 0xA0, 0x00, 0x00, 0x03, 0x96,
    0x04, 0x03, 0x00, 0x00, 0x00, 0x00, 0x68, 0x90, 0x00
};

// 测试用的通道号
static const uint8_t TEST_BASIC_CHANNEL = 0x00;
static const uint8_t TEST_LOGICAL_CHANNEL = 0x01;

// 测试用的P2参数
static const uint8_t TEST_P2_PARAM = 0x00;

// 测试超时时间(毫秒)
static const uint32_t TEST_TIMEOUT_MS = 5000;

// 测试重试次数
static const int TEST_RETRY_COUNT = 3;

} // namespace TmsSeTest

// 测试基类，提供通用的测试设置和清理
class TmsSeTestBase : public ::testing::Test {
protected:
    void SetUp() override;
    void TearDown() override;

    // 辅助方法
    bool isVectorEqual(const std::vector<uint8_t>& v1, const std::vector<uint8_t>& v2);
    std::string vectorToHexString(const std::vector<uint8_t>& data);
    void printTestInfo(const std::string& testName);
};

// 参数化测试的基类
template<typename T>
class TmsSeParameterizedTestBase : public ::testing::TestWithParam<T> {
protected:
    void SetUp() override;
    void TearDown() override;
};

// 测试夹具宏，用于简化测试类的定义
#define DEFINE_TEST_FIXTURE(ClassName, BaseClass) \
    class ClassName : public BaseClass { \
    protected: \
        void SetUp() override; \
        void TearDown() override; \
    }

// 测试断言宏，用于简化常见的断言
#define ASSERT_TMSSTATUS_SUCCESS(status) \
    ASSERT_EQ(TMSSTATUS_SUCCESS, status) << "Expected TMSSTATUS_SUCCESS but got " << status

#define EXPECT_TMSSTATUS_SUCCESS(status) \
    EXPECT_EQ(TMSSTATUS_SUCCESS, status) << "Expected TMSSTATUS_SUCCESS but got " << status

#define ASSERT_TEEC_SUCCESS(result) \
    ASSERT_EQ(TEEC_SUCCESS, result) << "Expected TEEC_SUCCESS but got " << result

#define EXPECT_TEEC_SUCCESS(result) \
    EXPECT_EQ(TEEC_SUCCESS, result) << "Expected TEEC_SUCCESS but got " << result

#define ASSERT_VECTOR_EQ(expected, actual) \
    ASSERT_TRUE(isVectorEqual(expected, actual)) \
        << "Expected: " << vectorToHexString(expected) \
        << ", Actual: " << vectorToHexString(actual)

#define EXPECT_VECTOR_EQ(expected, actual) \
    EXPECT_TRUE(isVectorEqual(expected, actual)) \
        << "Expected: " << vectorToHexString(expected) \
        << ", Actual: " << vectorToHexString(actual)

// 测试基类的实现
inline void TmsSeTestBase::SetUp() {
    printTestInfo("Setting up test");
}

inline void TmsSeTestBase::TearDown() {
    printTestInfo("Tearing down test");
}

inline bool TmsSeTestBase::isVectorEqual(const std::vector<uint8_t>& v1, const std::vector<uint8_t>& v2) {
    if (v1.size() != v2.size()) {
        return false;
    }
    return std::equal(v1.begin(), v1.end(), v2.begin());
}

inline std::string TmsSeTestBase::vectorToHexString(const std::vector<uint8_t>& data) {
    std::stringstream ss;
    ss << std::hex << std::uppercase << std::setfill('0');
    for (size_t i = 0; i < data.size(); ++i) {
        if (i > 0) ss << " ";
        ss << std::setw(2) << static_cast<int>(data[i]);
    }
    return ss.str();
}

inline void TmsSeTestBase::printTestInfo(const std::string& testName) {
    std::cout << "[TEST INFO] " << testName << std::endl;
}

template<typename T>
inline void TmsSeParameterizedTestBase<T>::SetUp() {
    printTestInfo("Setting up parameterized test");
}

template<typename T>
inline void TmsSeParameterizedTestBase<T>::TearDown() {
    printTestInfo("Tearing down parameterized test");
}

#endif // TMS_SE_TEST_COMMON_H
