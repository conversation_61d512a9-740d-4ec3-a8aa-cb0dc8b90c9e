/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#pragma once

#include <aidl/android/hardware/secure_element/BnSecureElement.h>
#include <aidl/android/hardware/secure_element/ISecureElementCallback.h>
#include <android/binder_manager.h>
#include <android/binder_process.h>
#include <log/log.h>
#include <thread>
#include <stdlib.h>
#include <string.h>
#ifdef CHECK_UNION_PARAM
#include <cutils/properties.h>
#endif

#include "TmsEse.h"
#include "TmsSeAdaptation.h"

namespace aidl {
namespace android {
namespace hardware {
namespace secure_element {

using ::aidl::android::hardware::secure_element::ISecureElementCallback;
using ::android::sp;
using ::ndk::ICInterface;
using ndk::ScopedAStatus;

struct SecureElement : public BnSecureElement {

    SecureElement();
    ::ndk::ScopedAStatus closeChannel(int8_t in_channelNumber) override;
    ::ndk::ScopedAStatus getAtr(std::vector<uint8_t> *_aidl_return) override;
    ::ndk::ScopedAStatus init(
        const std::shared_ptr <
        ::aidl::android::hardware::secure_element::ISecureElementCallback > &
        in_clientCallback) override;
    ::ndk::ScopedAStatus isCardPresent(bool *_aidl_return) override;
    ::ndk::ScopedAStatus openBasicChannel(
        const std::vector<uint8_t> &in_aid, int8_t in_p2,
        std::vector<uint8_t> *_aidl_return) override;
    ::ndk::ScopedAStatus openLogicalChannel(
        const std::vector<uint8_t> &in_aid, int8_t in_p2,
        ::aidl::android::hardware::secure_element::LogicalChannelResponse *
        _aidl_return) override;
    ::ndk::ScopedAStatus reset() override;
    ::ndk::ScopedAStatus transmit(const std::vector<uint8_t> &in_data,
                                  std::vector<uint8_t> *_aidl_return) override;
    static std::shared_ptr<ISecureElementCallback> mCallback;
};

}  // namespace secure_element
}  // namespace hardware
}  // namespace android
}  // namespace aidl
