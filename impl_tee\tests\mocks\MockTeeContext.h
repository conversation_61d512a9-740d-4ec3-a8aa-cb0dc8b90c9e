/*
 * Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.
 */

#ifndef MOCK_TEE_CONTEXT_H
#define MOCK_TEE_CONTEXT_H

#include <gmock/gmock.h>
#include <vector>
#include <memory>

#if defined(MTK_TRUSTONIC_TEE) || defined(ENABLE_OPTEE_FUNC)
#include "tee_client_api.h"
#else
#include "TEE_client_api.h"
#endif

/**
 * Mock TEE Context类
 * 用于模拟TEE客户端API的行为
 */
class MockTeeContext {
public:
    virtual ~MockTeeContext() = default;
    
    // TEE Context相关的Mock方法
    MOCK_METHOD(TEEC_Result, InitializeContext, (const char* name, TEEC_Context* context));
    MOCK_METHOD(void, FinalizeContext, (TEEC_Context* context));
    
    // TEE Session相关的Mock方法
    MOCK_METHOD(TEEC_Result, OpenSession, (TEEC_Context* context, TEEC_Session* session,
                                          const TEEC_UUID* destination, uint32_t connectionMethod,
                                          const void* connectionData, TEEC_Operation* operation,
                                          uint32_t* returnOrigin));
    MOCK_METHOD(void, CloseSession, (TEEC_Session* session));
    
    // TEE Operation相关的Mock方法
    MOCK_METHOD(TEEC_Result, InvokeCommand, (TEEC_Session* session, uint32_t commandID,
                                            TEEC_Operation* operation, uint32_t* returnOrigin));
    
    // TEE SharedMemory相关的Mock方法
    MOCK_METHOD(TEEC_Result, RegisterSharedMemory, (TEEC_Context* context, TEEC_SharedMemory* sharedMem));
    MOCK_METHOD(TEEC_Result, AllocateSharedMemory, (TEEC_Context* context, TEEC_SharedMemory* sharedMem));
    MOCK_METHOD(void, ReleaseSharedMemory, (TEEC_SharedMemory* sharedMem));
    
    // 获取单例实例
    static MockTeeContext* getInstance();
    static void setInstance(std::unique_ptr<MockTeeContext> instance);
    
private:
    static std::unique_ptr<MockTeeContext> s_instance;
};

/**
 * Mock TEE SE API类
 * 用于模拟TEE SE API的行为
 */
class MockTeeSeApi {
public:
    virtual ~MockTeeSeApi() = default;
    
    // SE Service相关的Mock方法
    MOCK_METHOD(TEEC_Result, SEServiceOpen, (void** seServiceHandle));
    MOCK_METHOD(void, SEServiceClose, (void* seServiceHandle));
    MOCK_METHOD(TEEC_Result, SEServiceGetReaders, (void* seServiceHandle, void** seReaderHandleList, uint32_t* seReaderHandleListLen));
    
    // SE Reader相关的Mock方法
    MOCK_METHOD(void, SEReaderGetProperties, (void* seReaderHandle, void* readerProperties));
    MOCK_METHOD(TEEC_Result, SEReaderGetName, (void* seReaderHandle, char* readerName, uint32_t* readerNameLen));
    MOCK_METHOD(TEEC_Result, SEReaderOpenSession, (void* seReaderHandle, void** seSessionHandle));
    MOCK_METHOD(void, SEReaderCloseSessions, (void* seReaderHandle));
    
    // SE Session相关的Mock方法
    MOCK_METHOD(TEEC_Result, SESessionGetATR, (void* seSessionHandle, uint8_t* atr, uint32_t* atrLen));
    MOCK_METHOD(TEEC_Result, SESessionIsClosed, (void* seSessionHandle));
    MOCK_METHOD(void, SESessionClose, (void* seSessionHandle));
    MOCK_METHOD(TEEC_Result, SESessionOpenBasicChannel, (void* seSessionHandle, const std::vector<uint8_t>& seAID, void** seChannelHandle, uint8_t p2));
    MOCK_METHOD(TEEC_Result, SESessionOpenLogicalChannel, (void* seSessionHandle, const std::vector<uint8_t>& seAID, void** seChannelHandle, uint8_t p2, uint8_t* channelNum));
    
    // SE Channel相关的Mock方法
    MOCK_METHOD(TEEC_Result, SEChannelSelectNext, (void* seChannelHandle));
    MOCK_METHOD(TEEC_Result, SEChannelGetSelectResponse, (void* seChannelHandle, uint8_t* response, uint32_t* responseLen));
    MOCK_METHOD(TEEC_Result, SEChannelTransmit, (void* seChannelHandle, const std::vector<uint8_t>& command, std::vector<uint8_t>& response));
    MOCK_METHOD(void, SEChannelClose, (void* seChannelHandle));
    
    // 获取单例实例
    static MockTeeSeApi* getInstance();
    static void setInstance(std::unique_ptr<MockTeeSeApi> instance);
    
private:
    static std::unique_ptr<MockTeeSeApi> s_instance;
};

/**
 * Mock配置管理类
 * 用于模拟配置文件读取行为
 */
class MockConfigManager {
public:
    virtual ~MockConfigManager() = default;
    
    MOCK_METHOD(bool, hasKey, (const std::string& key));
    MOCK_METHOD(std::string, getString, (const std::string& key, const std::string& defaultValue));
    MOCK_METHOD(unsigned int, getUnsigned, (const std::string& key, unsigned int defaultValue));
    MOCK_METHOD(std::vector<uint8_t>, getBytes, (const std::string& key));
    
    // 获取单例实例
    static MockConfigManager* getInstance();
    static void setInstance(std::unique_ptr<MockConfigManager> instance);
    
private:
    static std::unique_ptr<MockConfigManager> s_instance;
};

// 便利宏定义
#define MOCK_TEE_CONTEXT() MockTeeContext::getInstance()
#define MOCK_TEE_SE_API() MockTeeSeApi::getInstance()
#define MOCK_CONFIG_MANAGER() MockConfigManager::getInstance()

// 测试夹具基类，自动设置和清理Mock对象
class MockTestFixture : public ::testing::Test {
protected:
    void SetUp() override;
    void TearDown() override;
    
    std::unique_ptr<MockTeeContext> mockTeeContext_;
    std::unique_ptr<MockTeeSeApi> mockTeeSeApi_;
    std::unique_ptr<MockConfigManager> mockConfigManager_;
};

#endif // MOCK_TEE_CONTEXT_H
