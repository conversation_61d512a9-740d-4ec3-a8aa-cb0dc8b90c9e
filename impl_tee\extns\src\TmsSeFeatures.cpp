/*
 * Copyright (c) 2024 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "TmsSeGpApi.h"
#include "TmsSeFeatures.h"

#ifdef TAG
#undef TAG
#endif
#define TAG    "SeFeature"

static const uint8_t gCAFeatures[MAX_FEATURES_SUPPORT] = {1, 1, 1};  // TODO: set form MACRO or config
static uint8_t gFeatures[MAX_FEATURES_SUPPORT] = {0};

void clearFeatureList() {
    memset(gFeatures, 0x00, MAX_FEATURES_SUPPORT);
}

void compareAndSetFeatures() {
    tmsPrintHex("TA Feature: ", gFeatures, MAX_FEATURES_SUPPORT);
    for (int i = 0; i < MAX_FEATURES_SUPPORT; i++) {
        gFeatures[i] &= gCAFeatures[i];
    }
    tmsPrintHex("Common Feature: ", gFeatures, MAX_FEATURES_SUPPORT);
}

const uint8_t* getCAFeatures() {
    return gCAFeatures;
}

uint32_t getCAFeaturesLen() {
    return sizeof(gCAFeatures) / sizeof(uint8_t);
}

uint8_t* getFeatures() {
    return gFeatures;
}

uint32_t getFeaturesLen() {
    return sizeof(gFeatures) / sizeof(uint8_t);
}

bool isSupportFeature(TMS_TEE_FEATURE feature) {
    if (feature >= MAX_FEATURES_SUPPORT) {
        return false;
    }
    return gFeatures[feature] > 0;
}

uint8_t getFeatureVersion(TMS_TEE_FEATURE feature) {
    if (feature >= MAX_FEATURES_SUPPORT) {
        return 0;
    }
    return gFeatures[feature];
}

uint32_t getFeaturesNeededMemSize(uint32_t origin) {
    uint32_t ret = origin;
    if (origin < MAX_BUFFER && isSupportFeature(TEE_TRANSPORT_RULE)) {
        ret += LEN_TTR_HEAD;
        if (isSupportFeature(EXACT_TEE_RESULT)) {
            ret += LEN_TTR_HEAD + LEN_EXACT_TEE_RESULT;
        }
        if (isSupportFeature(TEE_LOG_TRANSPORT) | isSupportFeature(ENCRYPTED_TEE_LOG_TRANSPORT)) {
            ret += LEN_TTR_HEAD + LEN_LOG_BUFFER;
        }
    }
    return ret > MAX_BUFFER? MAX_BUFFER: ret;
}

static uint32_t parseLen(const uint8_t* p) {
    uint32_t len = *p;
    len = len << 8 | *(p + 1);
    return len;
}

static void printLinesFromBuffer(const char* buffer, const uint32_t len) {
    const char* start = buffer;
    for (size_t i = 0; i < len; ++i) {
        if (buffer[i] == '\0' || i == len - 1) {
            TMSLOG_I("TA-%s", start);
            if (buffer[i] == '\0' && buffer[i + 1] == '\0') {
                ++i;
            }
            start = &buffer[i + 1];
        }
    }
}

uint32_t parseBufferAndGetOriginData(void *dst, size_t dst_size, const uint8_t *src, size_t src_size) {
    uint32_t originDataLen = 0;
    uint32_t eResult = 0;
    if (!isSupportFeature(TEE_TRANSPORT_RULE)) {
        originDataLen = memscpy(dst, dst_size, src, src_size);
    } else {
        // tmsPrintHex("TTR: ", src, src_size);
        for (size_t i = 0; i < src_size;) {
            const uint8_t *p = src + i;
            uint8_t tag = *p;
            if (i + 2 >= src_size) {
                TMSLOG_E("%s: Failed to get Len of Tag %u", __func__, tag);
                break;
            }
            uint32_t len = parseLen(++p);
            uint8_t offset = 2;
            i += 1 + offset + len;
            if (i > src_size) {
                TMSLOG_E("%s: Failed to get all value of Tag %u, exceed %lu bytes!", __func__, tag, i - src_size);
                break;
            }
            switch (tag) {
                case TAG_ORIGIN_DATA:
                    originDataLen = memscpy(dst, dst_size, p + offset, len);
                    break;
                case TAG_EXACT_TEE_RESULT:
                    memscpy(&eResult, sizeof(eResult), p + offset, len);
                    if (0 != eResult) {
                        uint8_t err = eResult & 0x000000FF;
                        uint32_t msg = (eResult & 0x00FFFF00) >> 8;
                        TMSLOG_E("TMS TEE Result: 0x%08x, err = %d, msg = 0x%04x(%d)", eResult, err, msg, msg);
                    }
                    break;
                case TAG_TEE_LOG_TRANSPORT:
                case TAG_ENCRYPTED_TEE_LOG_TRANSPORT:
                    printLinesFromBuffer((char *)p + offset, len);
                    break;
                case TAG_INVAID_EMPTY:
                    break; // empty buffer
                default:
                    TMSLOG_E("%s: Invalid Tag %u", __func__, tag);
                    break;
            }
        }
    }
    return originDataLen;
}
