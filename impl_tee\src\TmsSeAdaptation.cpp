/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "TmsSeAdaptation.h"

#ifdef TAG
#undef TAG
#endif
#define TAG    "SeAdapt"

/* Use for TEE SE API Begin*/
static TEE_SEServiceHandle seServiceHandle = nullptr;
static TEE_SEReaderHandle seReaderHandles[MAX_READER_NUM];
static TEE_SEReaderHandle seReaderHandle = nullptr;
static TEE_SEReaderProperties seReaderProp;
static TEE_SESessionHandle seSessionHandle = nullptr;
static TEE_SEChannelHandle seChannelHandles[MAX_CHANNEL_NUM];
static uint8_t gOpenedChannelCnt = 0;
/* Use for TEE SE API End*/
TEEC_Result teeUnloadTmsEseSvc();
TEEC_Result teeInitTmsEseSvc();

static void tryRecovery() {
    // try reload TA when TA dead.
    teeUnloadTmsEseSvc();
    tmsesesvc_open(ESE_MODE_NORMAL);
    teeInitTmsEseSvc();
    TMSLOG_I("%s: Finished", __func__);
}

TEEC_Result teeUnloadTmsEseSvc() {
    // reset se api handlers
    memset(seChannelHandles, 0x00, sizeof(seChannelHandles));
    seSessionHandle = nullptr;
    memset(&seReaderProp, 0x00, sizeof(seReaderProp));
    seReaderHandle = nullptr;
    memset(seReaderHandles, 0x00, sizeof(seReaderHandles));
    seServiceHandle = nullptr;
    return tmsesesvc_close();
}

TEEC_Result teeInitTmsEseSvc() {
    TEEC_Result result = TEEC_SUCCESS;
    char seName[20];
    uint32_t nameLen = 20;
    uint32_t actualReaderNum = MAX_READER_NUM;
    TMSLOG_D("%s: Enter", __func__);

#ifdef MTK_TRUSTONIC_TEE
    if (!requestSpiClk()) {
        TMSLOG_E("%s: enable Spi Clk failed!!", __func__);
        goto exit;
    }
#endif

    result = TEE_SEServiceOpen(&seServiceHandle);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: TEE_SEServiceOpen failed, result = 0x%x", __func__, result);
        goto exit;
    }

    result = TEE_SEServiceGetReaders(seServiceHandle, seReaderHandles,
                                     &actualReaderNum);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: TEE_SEServiceGetReaders failed, result = 0x%x", __func__, result);
        goto exit;
    }

    for (int i = 0; i < actualReaderNum; i++) {
        if (seReaderHandles[i] == nullptr) {
            continue;
        }

        result = TEE_SEReaderGetName(seReaderHandles[i], seName, &nameLen);

        if (TEEC_SUCCESS == result && strcmp(seName, "eSE") == 0) {
            TMSLOG_D("%s: seReaderHandles[%d], eSE exist.", __func__, i);
            seReaderHandle = seReaderHandles[i];
            break;
        }
    }

    if (seReaderHandle != nullptr) {
        TEE_SEReaderGetProperties(seReaderHandle, &seReaderProp);
    } else {
        TMSLOG_E("%s: cannot find eSE reader.", __func__);
    }

exit:

    if (TEEC_ERROR_TARGET_DEAD == result) {
        TMSLOG_E("%s: Target dead during initializing!", __func__);
        tryRecovery();
    }

#ifdef MTK_TRUSTONIC_TEE
    if (!releaseSpiClk()) {
        TMSLOG_E("%s: disable Spi Clk failed!!", __func__);
    }
#endif
    TMSLOG_I("%s: Result = 0x%08X", __func__, result);
    return result;
}

static bool checkAndOpenSeSession() {
    TMSLOG_D("%s: Enter", __func__);

#ifdef MTK_TRUSTONIC_TEE
    if (!requestSpiClk()) {
        TMSLOG_E("%s: enable Spi Clk failed!!", __func__);
        return false;
    }
#endif

    TEEC_Result result = TEEC_ERROR_GENERIC;

    if ((seSessionHandle != nullptr)
        && (TEE_SESessionIsClosed(seSessionHandle) == TMS_TEE_SE_SESSION_OPEN)) {
        return true;
    } else {
        result = TEE_SEReaderOpenSession(seReaderHandle, &seSessionHandle);
    }

    if (TEEC_ERROR_TARGET_DEAD == result) {
        TMSLOG_E("%s: tmsesesvc not alive, connect to it!!!", __func__);
        tryRecovery();
    }

#ifdef MTK_TRUSTONIC_TEE
    if (TEEC_SUCCESS != result && !releaseSpiClk()) {
        TMSLOG_E("%s: disable Spi Clk failed!!", __func__);
    }
#endif
    TMSLOG_I("%s: Result = 0x%08X", __func__, result);
    return TEEC_SUCCESS == result;
}

static void closeSeSession() {

    if (0 == gOpenedChannelCnt) {
        TEE_SESessionClose(seSessionHandle);
        seSessionHandle = nullptr;
    }

#ifdef MTK_TRUSTONIC_TEE
    if (!releaseSpiClk()) {
        TMSLOG_E("%s: disable Spi Clk failed!!", __func__);
    }
#endif
    TMSLOG_D("%s: Finished", __func__);
}

void tmsSeChannelsRecClean() {
    for (uint8_t cnt = 0; cnt < MAX_CHANNEL_NUM; cnt++) {
        seChannelHandles[cnt] = nullptr;
    }

#ifdef MTK_TRUSTONIC_TEE
        releaseSpiClk(gOpenedChannelCnt);
#endif
    gOpenedChannelCnt = 0;
}

bool tmsIsSeInitialized() {
    return (seServiceHandle != nullptr) && (seReaderHandle != nullptr);
}

TMSSTATUS tmsSeInit() {
    TMSSTATUS status = TMS_FAILED;
    char seHalVersion[] = MW_VERSION;
    char seHalBuildTime[] = MW_BUILD_TIME;

    TMSLOG_I("TMS SE Hal Version is %s, Build time is %s", seHalVersion, seHalBuildTime);
    TEEC_Result result = tmsesesvc_open(ESE_MODE_NORMAL);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: tmsesesvc_open failed!", __func__);
        goto exit;
    }

    if (tmsIsSeInitialized()) {
        status = TMS_SUCCESS;
        goto exit;
    }

    result = teeInitTmsEseSvc();

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: teeInitTmsEseSvc failed!", __func__);
        goto exit;
    } else {
        status = TMS_SUCCESS;
    }

exit:
    TMSLOG_I("%s: Status = %d", __func__, status);
    return status;
}

void tmsSeDeInit() {
    TMSLOG_D("%s: Enter", __func__);

#ifdef MTK_TRUSTONIC_TEE
    if (!requestSpiClk()) {
        TMSLOG_E("%s: enable Spi Clk failed!!", __func__);
        return;
    }
#endif

    if (seSessionHandle != nullptr) {
        TEE_SESessionCloseChannels(seSessionHandle);
        tmsSeChannelsRecClean();
    }

    if (seSessionHandle != nullptr) {
        TEE_SESessionClose(seSessionHandle);
        seSessionHandle = nullptr;
    }

    for (int i = 0; i < MAX_READER_NUM; i++) {
        seReaderHandles[i] = nullptr;
    }

    seReaderHandle = nullptr;

    if (seServiceHandle != nullptr) {
        TEE_SEServiceClose(seServiceHandle);
        seServiceHandle = nullptr;
    }

#ifdef MTK_TRUSTONIC_TEE
    if (!releaseSpiClk()) {
        TMSLOG_E("%s: disable Spi Clk failed!!", __func__);
    }
#endif

    if (teeUnloadTmsEseSvc() != TEEC_SUCCESS) {
        TMSLOG_E("%s: teeUnloadTmsEseSvc Failed!!!", __func__);
    }

    TMSLOG_D("%s: Exit", __func__);
}

TMSSTATUS tmsSeGetAtr(std::vector<uint8_t> &atr) {
    TMSSTATUS status = TMS_FAILED;
    uint8_t rsp[APDUMAXLEN_R_APDU] = {0};
    uint32_t rspLen = APDUMAXLEN_R_APDU;

    if (!checkAndOpenSeSession()) {
        TMSLOG_E("%s: TEE_SEReaderOpenSession Failed!!!", __func__);
        return status;
    }

    TEEC_Result result = TEE_SESessionGetATR(seSessionHandle, rsp, &rspLen);
    closeSeSession();

    if (result == TEEC_SUCCESS && (rspLen != 0)) {
        atr.resize(rspLen);
        memcpy(atr.data(), rsp, rspLen);
        status = TMS_SUCCESS;
    } else if (TEEC_ERROR_TARGET_DEAD == result) {
        tryRecovery();
        status = TMS_SE_STATUS_TARGET_DEAD;
    }

    TMSLOG_I("%s: Status = %d", __func__, status);
    return status;
}

bool tmsIsSeCardPresent() {
    return seReaderProp.sePresent;
}

TMSSTATUS tmsSeTransmit(std::vector<uint8_t> &cmdApdu,
                        std::vector<uint8_t> &rspApdu) {
    TMSSTATUS status =  TMS_SUCCESS;
    TEEC_Result result = TEEC_SUCCESS;
    uint8_t channelNum = cmdApdu[0] & CLA_CHANNEL_MASK;
    uint8_t resp[APDUMAXLEN_R_APDU];
    uint32_t respLen = APDUMAXLEN_R_APDU;

    if (!seChannelHandles[channelNum]) {
        TMSLOG_E("%s: invailed channel!!!", __func__);
        return TMS_STATUS_INVALID_PARAMETERS;
    }

    if (cmdApdu.size() < MIN_APDU_LENGTH) {
        TMSLOG_E("%s: Invalid cmdApdu! len = %u.", __func__, (uint32_t)cmdApdu.size());
        return TMS_STATUS_INVALID_PARAMETERS;
    }

    tmsPrintHex("TX", cmdApdu.data(), cmdApdu.size());

    if (5 == cmdApdu.size() && cmdApdu[0] == 0x00 && cmdApdu[1] == 0xA4
        && cmdApdu[2] == 0x04 && cmdApdu[3] == 0x00 && cmdApdu[4] == 0x00) {
        TMSLOG_D("%s: TA will select null aid when closing basic channel.", __func__);
        resp[0] = 0x90;
        resp[1] = 0x00;
        respLen = 2;
    } else {
        result = TEE_SEChannelTransmit(seChannelHandles[channelNum], cmdApdu.data(), cmdApdu.size(),
                                       resp, &respLen);
    }

    if (TEEC_ERROR_TARGET_DEAD == result) {
        tryRecovery();
        status = TMS_SE_STATUS_TARGET_DEAD;
    }

    if (TEEC_SUCCESS != result) {
        TMSLOG_E("%s: transmit failed!!! result = 0x%08x", __func__, result);
        status =  TMS_FAILED;

        if (TEEC_ERROR_COMMUNICATION == result) {
            if (tmsSeReset() != TMS_SUCCESS) {
                TMSLOG_E("%s: Reset failed!!", __func__);
            }

            status = TMS_STATUS_COMMUNICATION_ERROR;
        } else {
            tmsSeCloseChannel(channelNum);
        }
    } else {
        rspApdu.resize(respLen);
        memcpy(rspApdu.data(), resp, respLen);
        tmsPrintHex("RX", rspApdu.data(), rspApdu.size());
    }

    TMSLOG_D("%s: Status = %d", __func__, status);
    return status;
}

TMSSTATUS tmsOpenLogicalChannel(std::vector<uint8_t> &aid, uint8_t p2,
                                uint8_t *channelNum, std::vector<uint8_t> &selectRsp) {
    TMSSTATUS status = TMS_FAILED;
    TEE_SEAID seAID;
    uint32_t selectRspLen;
    uint8_t actualNum = 0xFF;
    TEE_SEChannelHandle channelHandle = nullptr;
    if (!checkAndOpenSeSession()) {
        TMSLOG_E("%s: TEE_SEReaderOpenSession Failed!!!", __func__);
        return status;
    }

    seAID.bufferLen = aid.size();
    seAID.buffer = aid.data();
    selectRsp.resize(APDUMAXLEN_R_APDU);
    selectRspLen = selectRsp.size();
    tmsPrintHex("AID", seAID.buffer, seAID.bufferLen);
    TEEC_Result result = TMSTEE_SESessionOpenLogicalChannel(seSessionHandle, &seAID,
                         &channelHandle, p2, channelNum);

    if (TEEC_SUCCESS == result) {
        TMSLOG_I("%s: open logical channel %d success", __func__, *channelNum);
        actualNum = *channelNum;
        result = TEE_SEChannelGetSelectResponse(channelHandle, selectRsp.data(),
                                                &selectRspLen);
    }

    if (result != TEEC_SUCCESS) {
        TMSLOG_D("%s: failed!!! result = 0x%08x", __func__, result);
        TEE_SEChannelClose(channelHandle);
        *channelNum = 0xff;
        closeSeSession();
    } else {
        selectRsp.resize(selectRspLen);
        seChannelHandles[actualNum] = channelHandle;
        gOpenedChannelCnt++;
        tmsPrintHex("selectRsp", selectRsp.data(), selectRsp.size());
    }

    switch (result) {
        case TEEC_SUCCESS:
            status = TMS_SUCCESS;
            break;

        case TEEC_ERROR_ITEM_NOT_FOUND:
            status = TMS_SE_STATUS_ITEM_NOT_FOUND;
            break;

        case TEEC_ERROR_NOT_SUPPORTED:
            status = TMS_STATUS_UNSUPPORTED;
            break;

        case TEEC_ERROR_COMMUNICATION:

            /* if the SE is unresponsive, reset it */
            if (tmsSeReset() != TMS_SUCCESS) {
                TMSLOG_E("%s: tmsSeReset reset failed!!", __func__);
            }

            status = TMS_STATUS_COMMUNICATION_ERROR;
            break;

        case TEEC_ERROR_TARGET_DEAD:
            tryRecovery();
            status = TMS_SE_STATUS_TARGET_DEAD;
            break;

        default:
            status = TMS_FAILED;
            break;
    }

    TMSLOG_D("%s: status = %d", __func__, status);
    return status;
}

TMSSTATUS tmsOpenBasicChannel(std::vector<uint8_t> &aid, uint8_t p2,
                              std::vector<uint8_t> &selectRsp) {
    TMSSTATUS status = TMS_FAILED;
    TEE_SEAID seAID;
    uint32_t selectRspLen;
    TEE_SEChannelHandle channelHandle = nullptr;
    if (seChannelHandles[BASIC_CHANNLE_NUM]) {
        TMSLOG_E("%s: baisc channel open repeatedly", __func__);
        return TMS_STATUS_UNSUPPORTED;
    }
    if (!checkAndOpenSeSession()) {
        TMSLOG_E("%s: TEE_SEReaderOpenSession Failed!!!", __func__);
        return status;
    }

    seAID.bufferLen = aid.size();
    seAID.buffer = aid.data();
    selectRsp.resize(APDUMAXLEN_R_APDU);
    selectRspLen = selectRsp.size();
    tmsPrintHex("AID", seAID.buffer, seAID.bufferLen);
    TEEC_Result result = TMSTEE_SESessionOpenBasicChannel(seSessionHandle, &seAID,
                         &channelHandle, p2);

    if (TEEC_SUCCESS == result) {
        result = TEE_SEChannelGetSelectResponse(channelHandle, selectRsp.data(),
                                                &selectRspLen);
    }

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: failed!!! result = 0x%08x", __func__, result);
        TEE_SEChannelClose(channelHandle);
        closeSeSession();
    } else {
        selectRsp.resize(selectRspLen);
        seChannelHandles[BASIC_CHANNLE_NUM] = channelHandle;
        gOpenedChannelCnt++;
        tmsPrintHex("selectRsp", selectRsp.data(), selectRsp.size());
    }

    switch (result) {
        case TEEC_SUCCESS:
            status = TMS_SUCCESS;
            break;

        case TEEC_ERROR_ITEM_NOT_FOUND:
            status = TMS_SE_STATUS_ITEM_NOT_FOUND;
            break;

        case TEEC_ERROR_NOT_SUPPORTED:
            status = TMS_STATUS_UNSUPPORTED;
            break;

        case TEEC_ERROR_COMMUNICATION:

            /* if the SE is unresponsive, reset it */
            if (tmsSeReset() != TMS_SUCCESS) {
                TMSLOG_E("%s: tmsSeReset reset failed!!", __func__);
            }

            status = TMS_STATUS_COMMUNICATION_ERROR;
            break;

        case TEEC_ERROR_TARGET_DEAD:
            tryRecovery();
            status = TMS_SE_STATUS_TARGET_DEAD;
            break;

        default:
            status = TMS_FAILED;
            break;
    }

    TMSLOG_D("%s: status = %d", __func__, status);
    return status;
}

TMSSTATUS tmsSeCloseChannel(uint8_t channelNum) {
    TMSSTATUS status = TMS_FAILED;

    if ((channelNum >= MAX_CHANNEL_NUM) ||
        (seChannelHandles[channelNum] == nullptr)) {
        TMSLOG_E("%s: invalid channel!!!", __func__);
        status = TMS_STATUS_INVALID_PARAMETERS;
    } else {
        TEE_SEChannelClose(seChannelHandles[channelNum]);
        seChannelHandles[channelNum] = nullptr;

        if (gOpenedChannelCnt > 0) {
            gOpenedChannelCnt--;
        }

        closeSeSession();
        status = TMS_SUCCESS;
    }

    TMSLOG_I("%s: status = %d", __func__, status);
    return status;
}

TMSSTATUS tmsSeReset() {
    TMSSTATUS status = TMS_FAILED;
    TMSLOG_D("%s: Enter", __func__);

    if (!checkAndOpenSeSession()) {
        TMSLOG_E("%s: TEE_SEReaderOpenSession Failed!!!", __func__);
        return status;
    }

    TEEC_Result result = tmsesesvc_generic(TMS_GENERIC_CMD_RESET, 0, nullptr);

    if (result == TEEC_SUCCESS) {
        tmsSeChannelsRecClean();
        status = TMS_SUCCESS;
    } else {
        TMSLOG_E("%s: failed!! result = 0x%08x", __func__, result);
        status = TMS_FAILED;
    }

    closeSeSession();

    if (TEEC_ERROR_TARGET_DEAD == result) {
        tryRecovery();
        status = TMS_SE_STATUS_TARGET_DEAD;
    }

    TMSLOG_D("%s: status = %d", __func__, status);
    return status;
}

TMSSTATUS tmsSeHardReset() {
    TMSSTATUS status;

#ifdef MTK_TRUSTONIC_TEE
    if (!hardReset()) {
        return TMS_FAILED;
    }

    if (!requestSpiClk()) {
        TMSLOG_E("%s: enable Spi Clk failed!!", __func__);
        return TMS_FAILED;
    }

    status = TMS_SUCCESS;
#endif
    TEEC_Result result = tmsesesvc_generic(TMS_GENERIC_CMD_HARD_RESET, 1, nullptr);

    if (TEEC_SUCCESS == result) {
        tmsSeChannelsRecClean();
        status = TMS_SUCCESS;
    } else {
        TMSLOG_E("%s: failed!! result = 0x%08x", __func__, result);
        status = TMS_FAILED;
    }

#ifdef MTK_TRUSTONIC_TEE
    if (!releaseSpiClk()) {
        TMSLOG_E("%s: disable Spi Clk failed!!", __func__);
    }
#endif

    if (TEEC_ERROR_TARGET_DEAD == result) {
        tryRecovery();
        status = TMS_SE_STATUS_TARGET_DEAD;
    }

    TMSLOG_I("%s: status = %d", __func__, status);
    return status;
}

TMSSTATUS tmsSeStatusCheck(SeCheckMode mode) {
    TMSSTATUS status = TMS_FAILED;

#ifdef MTK_TRUSTONIC_TEE
    if (!requestSpiClk()) {
        TMSLOG_E("%s: enable Spi Clk failed!!", __func__);
        return status;
    }
#endif

    TEEC_Result result = tmsesesvc_generic(TMS_GENERIC_CMD_SE_STATUS_CHECK, mode, nullptr);

    if (TEEC_SUCCESS == result) {
        status = TMS_SUCCESS;
    } else if (TEEC_ERROR_TARGET_DEAD == result) {
        tryRecovery();
        status = TMS_SE_STATUS_TARGET_DEAD;
    }

#ifdef MTK_TRUSTONIC_TEE
    if (!releaseSpiClk()) {
        TMSLOG_E("%s: disable Spi Clk failed!!", __func__);
    }
#endif

    TMSLOG_I("%s: status = %d", __func__, status);
    return status;
}

bool tmsSeUpdateOpenSession(TEE_SESessionHandle *session) {
    TMSLOG_D("%s: Enter", __func__);

#ifdef MTK_TRUSTONIC_TEE
    if (!requestSpiClk()) {
        TMSLOG_E("%s: enable Spi Clk failed!!", __func__);
        return false;
    }
#endif

    TEEC_Result result = TEE_SEReaderOpenSession(seReaderHandle, session);

    if (TEEC_ERROR_TARGET_DEAD == result) {
        TMSLOG_E("%s: tmsesesvc not alive, connect to it!!!", __func__);
        tryRecovery();
    }

#ifdef MTK_TRUSTONIC_TEE
    if (TEEC_SUCCESS != result && !releaseSpiClk()) {
        TMSLOG_E("%s: disable Spi Clk failed!!", __func__);
    }
#endif

    TMSLOG_I("%s: Exit, result = 0x%08X", __func__, result);
    return TEEC_SUCCESS == result;
}

void tmsSeUpdateCloseSession(TEE_SESessionHandle session) {
    TMSLOG_D("%s: Enter", __func__);
    TEE_SESessionClose(session);

#ifdef MTK_TRUSTONIC_TEE
    if (!releaseSpiClk()) {
        TMSLOG_E("%s: disable Spi Clk failed!!", __func__);
    }
#endif

    TMSLOG_I("%s: Exit", __func__);
}
