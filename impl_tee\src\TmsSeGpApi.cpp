/*
 * Copyright (c) 2021 Tsingteng MicroSystem
 *
 * All rights are reserved. Reproduction in whole or in part is
 * prohibited without the written consent of the copyright owner.
 *
 * Tsingteng reserves the right to make changes without notice at any time.
 *
 * Tsingteng makes no warranty, expressed, implied or statutory, including but
 * not limited to any implied warranty of merchantability or fitness for any
 * particular purpose, or that the use will not infringe any third party patent,
 * copyright or trademark. Tsingteng must not be liable for any loss or damage
 * arising from its use.
 */

#include "TmsSeGpApi.h"

#ifdef TAG
#undef TAG
#endif
#define TAG    "SeGpApi"

/****** Variables for TEE Context ******/
static const uint8_t OPMEM_IND = 3;
static TEEC_Result result = TEEC_SUCCESS;
static TEEC_Context context;
static TEEC_Session session_svc;
static TEEC_Operation operation;
static TEEC_SharedMemory inputSM;
static TEEC_SharedMemory outputSM;

// for tmsesesvc TA
static bool contextInitialised = false;
static bool svcSessionOpened = false;
static bool eseInitialised = false;
uint32_t clientID = 0;

static SEParams seParams;

size_t memscpy(void *dst, size_t dst_size, const void *src, size_t src_size) {
    size_t min_size = (dst_size < src_size) ? dst_size : src_size;

    if (dst == nullptr || src == nullptr) {
        TMSLOG_E("%s: Pointer is nullptr, dst = %p, src = %p", __func__, dst, src);
        return -1;
    }

    memcpy(dst, src, min_size);
    return min_size;
}

void initPlatEsePropFromConfig(SEParams *pParams) {
    pParams->pType = (uint8_t)ConfigGetUnsigned(NAME_ESE_PROP_PLAT_TYPE,
                     strlen(NAME_ESE_PROP_PLAT_TYPE), 0xFF);

    if (pParams->pType == QCOM) {
        pParams->prop = getQcomPropFromConfig();
    } else if (pParams->pType == MTK) {
        pParams->prop = getMtkPropFromConfig();
    }

    // spi common config
    pParams->prop.DevId = ConfigGetUnsigned(NAME_ESE_PROP_SPI_DEVICE_ID, strlen(NAME_ESE_PROP_SPI_DEVICE_ID), 0xFF);
    pParams->prop.spiAdjust = ConfigGetUnsigned(NAME_ESE_PROP_SPI_ADJUST_FREQ, strlen(NAME_ESE_PROP_SPI_ADJUST_FREQ), 0);
    pParams->prop.spiFreqMinLimit = ConfigGetUnsigned(NAME_ESE_PROP_SPI_FREQ_MIN_LIMIT, strlen(NAME_ESE_PROP_SPI_FREQ_MIN_LIMIT), 0);
    pParams->prop.spiFreqMaxLimit = ConfigGetUnsigned(NAME_ESE_PROP_SPI_FREQ_MAX_LIMIT, strlen(NAME_ESE_PROP_SPI_FREQ_MAX_LIMIT), 0);
}

void initSEParams(SeInitMode initMode) {
    uint8_t log_level = ConfigGetUnsigned(NAME_TMS_HAL_LOG_LEVEL, strlen(NAME_TMS_HAL_LOG_LEVEL), 0x04);
    tmsSetLogLevel(log_level);
    seParams.mode = initMode;
    seParams.debugLevel = ConfigGetUnsigned(NAME_TMS_TA_LOG_LEVEL, strlen(NAME_TMS_TA_LOG_LEVEL), 0x34);

    if (seParams.debugLevel < 0) {
        seParams.debugLevel = 0;
    }

    seParams.maxWriteRetryCnt = 10;
    seParams.writeRetryTime = 1000;
    seParams.maxReadRetryCnt = 2000;
    seParams.readRetryTime = 1000;
    seParams.maxIFSD = 258;
    //Rule 6.4 — After the interface device has failed a maximum of three times
    //           in succession to reach the intended resynchronization by transmitting
    //           S(RESYNCH request), it performs either a warm reset or a deactivation.
    seParams.maxRecoveryCnt = 3;
    seParams.maxBlkRetryCnt = 3;
    seParams.maxWTXCnt = 30;
    seParams.readTimeout = 10;
    initPlatEsePropFromConfig(&seParams);
    memscpy(seParams.features, sizeof(seParams.features), getCAFeatures(), getCAFeaturesLen());
}

static TEEC_Result getOutputMemAndInvoke(void *output, uint32_t *len, uint32_t commandId) {
    uint8_t *tmpBuffer = nullptr;
    uint32_t tmpBufferLen = APDULEN_R_STATUS;
    uint32_t memsize = 0;

    if (nullptr == output || nullptr == len || 0 == *len) {
        TMSLOG_D("%s: getting default output buffer", __func__);
        tmpBuffer = (uint8_t *)malloc(tmpBufferLen * sizeof(uint8_t));
        if (nullptr == tmpBuffer) {
            TMSLOG_E("%s: malloc tmpBuffer failed!", __func__);
            return TEEC_ERROR_OUT_OF_MEMORY;
        }
        output = tmpBuffer;
        len = &tmpBufferLen;
    }

    outputSM.flags  = TEEC_MEM_OUTPUT;
    outputSM.size   = getFeaturesNeededMemSize(*len);
    result = TEEC_AllocateSharedMemory(&context, &outputSM);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: AllocateSharedMemory failed, result = 0x%08X", __func__, result);
        goto exit;
    }

    memset(outputSM.buffer, 0x0, outputSM.size);
    operation.params[OPMEM_IND].memref.parent = &outputSM;
    operation.params[OPMEM_IND].memref.offset = 0;
    operation.params[OPMEM_IND].memref.size = outputSM.size;

    result = TEEC_InvokeCommand(&session_svc, commandId, &operation, nullptr);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    }

    memsize = (uint32_t)operation.params[OPMEM_IND].memref.size;
    *len = parseBufferAndGetOriginData(output, *len, (uint8_t *)outputSM.buffer, memsize);
exit:

    if (nullptr != tmpBuffer) {
        free(tmpBuffer);
    }

    TEEC_ReleaseSharedMemory(&outputSM);
    return result;
}

TEEC_Result tmsesesvc_generic(uint32_t req, uint32_t val, TmsCapsule *inOut) {
    TMSLOG_D("%s: Enter", __func__);

    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d"
                 , __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    TMSLOG_I("%s: generic cmd = %u, val = %u", __func__, req, val);
    //check req
    operation.paramTypes = TEEC_PARAM_TYPES(TEEC_VALUE_INPUT,
                                            TEEC_MEMREF_PARTIAL_INPUT,
                                            TEEC_NONE,
                                            TEEC_MEMREF_PARTIAL_OUTPUT);
    //set input req and value.
    operation.params[0].value.a = req;
    operation.params[0].value.b = val;

    if (inOut != nullptr && inOut->pData != nullptr && inOut->len != 0) {
        inputSM.flags  = TEEC_MEM_INPUT;
        inputSM.size = inOut->len;
        result = TEEC_AllocateSharedMemory(&context, &inputSM);
        if (result != TEEC_SUCCESS) {
            TMSLOG_E("%s: AllocateSharedMemory for generic rsp failed, result = 0x%08X", __func__, result);
            return result;
        }

        memset(inputSM.buffer, 0x0, inputSM.size);
        memcpy(inputSM.buffer, inOut->pData, inOut->len);
        operation.params[1].memref.parent = &inputSM;
        operation.params[1].memref.offset = 0;
        operation.params[1].memref.size   = inputSM.size;
        result = getOutputMemAndInvoke(inOut->pData, &inOut->len, TMS_CMD_SE_GENERIC);
    } else {
        operation.paramTypes = TEEC_PARAM_TYPES(TEEC_VALUE_INPUT,
                                                TEEC_NONE,
                                                TEEC_NONE,
                                                TEEC_MEMREF_PARTIAL_OUTPUT);
        result = getOutputMemAndInvoke(nullptr, nullptr, TMS_CMD_SE_GENERIC);
    }

    if (inputSM.buffer != nullptr) {
        TEEC_ReleaseSharedMemory(&inputSM);
    }

    return result;
}

TEEC_Result tmsesesvc_open(SeInitMode initMode) {
    TMSLOG_D("%s: Enter", __func__);

    if (contextInitialised && svcSessionOpened && eseInitialised) {
        TMSLOG_D("%s: tmsesesvc already opened.", __func__);
        return TEEC_SUCCESS;
    }

    if (!contextInitialised) {
        result = TEEC_InitializeContext(nullptr, &context);

        if (result != TEEC_SUCCESS) {
            TMSLOG_E("%s: TEEC_InitializeContext failed, result = 0x%08X", __func__,
                     result);
            return result;
        }

        contextInitialised = true;
    }

    if (!svcSessionOpened) {
        result = TEEC_OpenSession(&context, &session_svc, &gpTmsSvcUUID,
                                  TEEC_LOGIN_USER, nullptr,
                                  nullptr, nullptr);

        if (result != TEEC_SUCCESS) {
            TMSLOG_E("%s: TEEC_OpenSession failed, result = 0x%08X", __func__, result);
            return tmsesesvc_close();
        }

        svcSessionOpened = true;
    }

    //Init and set eSE Config
    if (!eseInitialised) {
        initSEParams(initMode);
        operation.paramTypes = TEEC_PARAM_TYPES(
                           TEEC_VALUE_INPUT,
                           TEEC_MEMREF_PARTIAL_INPUT, // Input config struct buffer
                           TEEC_NONE,
                           TEEC_MEMREF_PARTIAL_OUTPUT);

        //set input req and value.
        operation.params[0].value.a = TMS_GENERIC_CMD_INIT_CTX;
        operation.params[0].value.b = 1;

        //Allocate Shared Memory for input
        inputSM.size = sizeof(seParams);
        inputSM.flags = TEEC_MEM_INPUT;
        result = TEEC_AllocateSharedMemory(&context, &inputSM);

        if (result != TEEC_SUCCESS) {
            TMSLOG_E("%s: AllocateSharedMemory for input failed, result = 0x%08X", __func__,
                     result);
            return tmsesesvc_close();
        }

        memscpy(inputSM.buffer, inputSM.size, (void *) &seParams, sizeof(seParams));
        operation.params[1].memref.parent = &inputSM;
        operation.params[1].memref.offset = 0;
        operation.params[1].memref.size = sizeof(seParams);

        clearFeatureList();
        uint32_t len = getFeaturesLen();
        result = getOutputMemAndInvoke(getFeatures(), &len, TMS_CMD_SE_GENERIC);

        if (result != TEEC_SUCCESS) {
            TMSLOG_E("%s: Init SE config failed, result = 0x%08X", __func__, result);
            TEEC_ReleaseSharedMemory(&inputSM);
            return tmsesesvc_close();
        }

        compareAndSetFeatures();

        TEEC_ReleaseSharedMemory(&inputSM);
        eseInitialised = true;
    }

    return result;
}

TEEC_Result tmsesesvc_close() {
    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: ERROR happened, close everything, result = 0x%08X", __func__, result);
    }

    if (svcSessionOpened) {
        TEEC_CloseSession(&session_svc);
    }

    if (contextInitialised) {
        TEEC_FinalizeContext(&context);
    }

    eseInitialised = false;
    svcSessionOpened = false;
    contextInitialised = false;
    return result;
}

TEEC_Result TEE_SEServiceOpen(TEE_SEServiceHandle *seServiceHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d"
                 , __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seServiceHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);

    uint32_t len = sizeof(*seServiceHandle);
    result = getOutputMemAndInvoke(seServiceHandle, &len, GPTMS_CMD_SE_SERVICE_OPEN);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: failed", __func__);
    } else {

        if (clientID == 0) {
            // TEE_GenerateRandom(&clientID, sizeof(clientID));
            clientID = 1;
        }

    }

    return result;
}

void TEE_SEServiceClose(TEE_SEServiceHandle seServiceHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d"
                 , __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seServiceHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seServiceHandle >> 32;
    operation.params[0].value.b = (size_t)seServiceHandle;
    operation.params[1].value.a = clientID;
    result = getOutputMemAndInvoke(nullptr, nullptr, GPTMS_CMD_SE_SERVICE_CLOSE);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: TEE_SEServiceClose failed", __func__);
    }

    clientID = 0;
    return;
}

TEEC_Result TEE_SEServiceGetReaders(TEE_SEServiceHandle seServiceHandle,
                                    TEE_SEReaderHandle *seReaderHandleList, uint32_t *seReaderHandleListLen) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seServiceHandle == nullptr || seReaderHandleList == nullptr
        || seReaderHandleListLen == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_VALUE_INOUT,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seServiceHandle >> 32;
    operation.params[0].value.b = (size_t)seServiceHandle;
    operation.params[1].value.a = *seReaderHandleListLen;
    //Allocate Shared Memory for input
    uint32_t listLen = (*seReaderHandleListLen) * sizeof(TEE_SEReaderHandle);
    result = getOutputMemAndInvoke(seReaderHandleList, &listLen, GPTMS_CMD_SE_SERVICE_GET_READERS);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    }

    return result;
}

void TEE_SEReaderGetProperties(TEE_SEReaderHandle seReaderHandle,
                               TEE_SEReaderProperties *readerProperties) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d"
                 , __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seReaderHandle == nullptr || readerProperties == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seReaderHandle >> 32;
    operation.params[0].value.b = (size_t)seReaderHandle;

    uint32_t len = sizeof(TEE_SEReaderProperties);
    result = getOutputMemAndInvoke(readerProperties, &len, GPTMS_CMD_SE_READER_GET_PROPERTIES);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    }

    return;
}

TEEC_Result TEE_SEReaderGetName(TEE_SEReaderHandle seReaderHandle,
                                char *readerName, uint32_t *readerNameLen) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d"
                 , __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }


    if (seReaderHandle == nullptr || readerName == nullptr
        || readerNameLen == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seReaderHandle >> 32;
    operation.params[0].value.b = (size_t)seReaderHandle;

    result = getOutputMemAndInvoke(readerName, readerNameLen, GPTMS_CMD_SE_READER_GET_NAME);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    }

    return result;
}

TEEC_Result TEE_SEReaderOpenSession(TEE_SEReaderHandle seReaderHandle,
                                    TEE_SESessionHandle *seSessionHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seReaderHandle == nullptr || seSessionHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seReaderHandle >> 32;
    operation.params[0].value.b = (size_t)seReaderHandle;
    operation.params[1].value.a = clientID;
    uint32_t len = sizeof(TEE_SESessionHandle);
    result = getOutputMemAndInvoke(seSessionHandle, &len, GPTMS_CMD_SE_READER_OPEN_SESSION);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    }

    return result;
}

void TEE_SEReaderCloseSessions(TEE_SEReaderHandle seReaderHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seReaderHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seReaderHandle >> 32;
    operation.params[0].value.b = (size_t)seReaderHandle;
    operation.params[1].value.a = clientID;
    result = getOutputMemAndInvoke(nullptr, nullptr, GPTMS_CMD_SE_READER_CLOSE_SESSIONS);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
        goto exit;
    }

exit:
    return;
}

TEEC_Result TEE_SESessionGetATR(TEE_SESessionHandle seSessionHandle, void *atr,
                                uint32_t *atrLen) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }


    if (seSessionHandle == nullptr || atr == nullptr || atrLen == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seSessionHandle >> 32;
    operation.params[0].value.b = (size_t)seSessionHandle;

    result = getOutputMemAndInvoke(atr, atrLen, GPTMS_CMD_SE_SESSION_GET_ATR);

    return result;
}

TEEC_Result TEE_SESessionIsClosed(TEE_SESessionHandle seSessionHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seSessionHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seSessionHandle >> 32;
    operation.params[0].value.b = (size_t)seSessionHandle;
    result = getOutputMemAndInvoke(nullptr, nullptr, GPTMS_CMD_SE_SESSION_IS_CLOSED);

    if (result != TEEC_SUCCESS && result != TMS_TEE_SE_SESSION_OPEN) {
        TMSLOG_E("%s: failed", __func__);
        goto exit;
    }

exit:
    TMSLOG_I("%s: Result = 0x%08X", __func__, result);
    return result;
}

void TEE_SESessionClose(TEE_SESessionHandle seSessionHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seSessionHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seSessionHandle >> 32;
    operation.params[0].value.b = (size_t)seSessionHandle;
    result = getOutputMemAndInvoke(nullptr, nullptr, GPTMS_CMD_SE_SESSION_CLOSE);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
        goto exit;
    }

exit:
    return;
}

void TEE_SESessionCloseChannels(TEE_SESessionHandle seSessionHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seSessionHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seSessionHandle >> 32;
    operation.params[0].value.b = (size_t)seSessionHandle;
    result = getOutputMemAndInvoke(nullptr, nullptr, GPTMS_CMD_SE_SESSION_CLOSE_CHANNELS);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
        goto exit;
    }

exit:
    return;
}

TEEC_Result TMSTEE_SESessionOpenBasicChannel(TEE_SESessionHandle
        seSessionHandle,
        TEE_SEAID *seAID, TEE_SEChannelHandle *seChannelHandle, uint8_t p2) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: Transceive Failed, contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seSessionHandle == nullptr || seAID == nullptr
        || seChannelHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_MEMREF_PARTIAL_INPUT,
                               TEEC_VALUE_INPUT,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seSessionHandle >> 32;
    operation.params[0].value.b = (size_t)seSessionHandle;

    if (0 == seAID->bufferLen || nullptr == seAID->buffer) {
        TMSLOG_I("%s: Basic aid=null", __func__);
        operation.paramTypes = TEEC_PARAM_TYPES(
                                   TEEC_VALUE_INPUT,
                                   TEEC_NONE,
                                   TEEC_VALUE_INPUT,
                                   TEEC_MEMREF_PARTIAL_OUTPUT);
    } else {
        //Allocate Shared Memory for input
        inputSM.size = seAID->bufferLen;
        inputSM.flags = TEEC_MEM_INPUT | TEEC_MEM_OUTPUT;
        result = TEEC_AllocateSharedMemory(&context, &inputSM);

        if (result != TEEC_SUCCESS) {
            TMSLOG_E("%s: Allocate SharedMemory for input failed, result = 0x%08X",
                     __func__, result);
            return result;
        }

        memset(inputSM.buffer, 0x0, inputSM.size);
        memscpy(inputSM.buffer, inputSM.size, seAID->buffer, seAID->bufferLen);
        operation.params[1].memref.parent = &inputSM;
        operation.params[1].memref.offset = 0;
        operation.params[1].memref.size = inputSM.size;
    }

    operation.params[2].value.a = p2;
    uint32_t len = sizeof(TEE_SEChannelHandle);
    result = getOutputMemAndInvoke(seChannelHandle, &len, TMS_CMD_SE_OPEN_BASIC_CHANNEL);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    }

    TEEC_ReleaseSharedMemory(&inputSM);
    return result;
}

TEEC_Result TMSTEE_SESessionOpenLogicalChannel(TEE_SESessionHandle
        seSessionHandle,
        TEE_SEAID *seAID, TEE_SEChannelHandle *seChannelHandle, uint8_t p2,
        uint8_t *channelNum) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: Transceive Failed, contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }


    if (seSessionHandle == nullptr || seAID == nullptr
        || seChannelHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_MEMREF_PARTIAL_INPUT,
                               TEEC_VALUE_INOUT,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seSessionHandle >> 32;
    operation.params[0].value.b = (size_t)seSessionHandle;

    if (0 == seAID->bufferLen || nullptr == seAID->buffer) {
        TMSLOG_E("%s: Logical aid=null", __func__);
        operation.paramTypes = TEEC_PARAM_TYPES(
                                   TEEC_VALUE_INPUT,
                                   TEEC_NONE,
                                   TEEC_VALUE_INOUT,
                                   TEEC_MEMREF_PARTIAL_OUTPUT);
    } else {
        //Allocate Shared Memory for input
        inputSM.size = seAID->bufferLen;
        inputSM.flags = TEEC_MEM_INPUT | TEEC_MEM_OUTPUT;
        result = TEEC_AllocateSharedMemory(&context, &inputSM);

        if (result != TEEC_SUCCESS) {
            TMSLOG_E("%s: Allocate SharedMemory for input failed, result = 0x%08X",
                     __func__, result);
            return result;
        }

        memset(inputSM.buffer, 0x0, inputSM.size);
        memscpy(inputSM.buffer, inputSM.size, seAID->buffer, seAID->bufferLen);
        operation.params[1].memref.parent = &inputSM;
        operation.params[1].memref.offset = 0;
        operation.params[1].memref.size = inputSM.size;
    }

    operation.params[2].value.a = p2;
    operation.params[2].value.b = *channelNum;
    uint32_t len = sizeof(TEE_SEChannelHandle);
    result = getOutputMemAndInvoke(seChannelHandle, &len, TMS_CMD_SE_OPEN_LOGICAL_CHANNEL);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    } else {
        *channelNum = operation.params[2].value.b;
    }

    TEEC_ReleaseSharedMemory(&inputSM);
    return result;
}

void TEE_SEChannelClose(TEE_SEChannelHandle seChannelHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: Transceive Failed, contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seChannelHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seChannelHandle >> 32;
    operation.params[0].value.b = (size_t)seChannelHandle;
    result = getOutputMemAndInvoke(nullptr, nullptr, GPTMS_CMD_SE_CHANNEL_CLOSE);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
        goto exit;
    }

exit:
    return;
}

TEEC_Result TEE_SEChannelSelectNext(TEE_SEChannelHandle seChannelHandle) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: Transceive Failed, contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seChannelHandle == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seChannelHandle >> 32;
    operation.params[0].value.b = (size_t)seChannelHandle;
    result = getOutputMemAndInvoke(nullptr, nullptr, GPTMS_CMD_SE_CHANNEL_SELECT_NEXT);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
        goto exit;
    }

exit:
    return result;
}

TEEC_Result TEE_SEChannelGetSelectResponse(TEE_SEChannelHandle seChannelHandle,
        void *response, uint32_t *responseLen) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: Transceive Failed, contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seChannelHandle == nullptr || response == nullptr
        || responseLen == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_NONE,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seChannelHandle >> 32;
    operation.params[0].value.b = (size_t)seChannelHandle;
    result = getOutputMemAndInvoke(response, responseLen, GPTMS_CMD_SE_CHANNEL_GET_SELECT_RESPONSE);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    }

    return result;
}

TEEC_Result TEE_SEChannelGetResponseLength(TEE_SEChannelHandle seChannelHandle,
        uint32_t *responseLen) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seChannelHandle == nullptr || responseLen == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_VALUE_OUTPUT,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seChannelHandle >> 32;
    operation.params[0].value.b = (size_t)seChannelHandle;
    result = getOutputMemAndInvoke(nullptr, nullptr, GPTMS_CMD_SE_CHANNEL_GET_RESPONSE_LENGTH);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
        goto exit;
    }

    *responseLen = operation.params[1].value.a;
exit:
    return result;
}

TEEC_Result TEE_SEChannelTransmit(TEE_SEChannelHandle seChannelHandle,
                                  void *command, uint32_t cmdLen, void *response, uint32_t *respLen) {
    if (!contextInitialised || !svcSessionOpened || !eseInitialised) {
        TMSLOG_W("%s: Transceive Failed, contextInitialised = %d, svcSessionOpened = %d, eseInitialised = %d",
                 __func__, contextInitialised, svcSessionOpened, eseInitialised);
    }

    if (seChannelHandle == nullptr || command == nullptr || response == nullptr
        || respLen == nullptr) {
        TMSLOG_E("%s: Invailed Parameters.", __func__);
        return TEEC_ERROR_BAD_PARAMETERS;
    }

    operation.paramTypes = TEEC_PARAM_TYPES(
                               TEEC_VALUE_INPUT,
                               TEEC_MEMREF_PARTIAL_INPUT,
                               TEEC_NONE,
                               TEEC_MEMREF_PARTIAL_OUTPUT);
    operation.params[0].value.a = (size_t)seChannelHandle >> 32;
    operation.params[0].value.b = (size_t)seChannelHandle;
    //Allocate Shared Memory for input
    inputSM.size = cmdLen;
    inputSM.flags = TEEC_MEM_INPUT | TEEC_MEM_OUTPUT;
    result = TEEC_AllocateSharedMemory(&context, &inputSM);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s: Allocate SharedMemory for input failed, result = 0x%08X",
                 __func__, result);
        return result;
    }

    memset(inputSM.buffer, 0x0, inputSM.size);
    memscpy(inputSM.buffer, inputSM.size, command, cmdLen);
    operation.params[1].memref.parent = &inputSM;
    operation.params[1].memref.offset = 0;
    operation.params[1].memref.size = inputSM.size;
    result = getOutputMemAndInvoke(response, respLen, GPTMS_CMD_SE_CHANNEL_TRANSMIT);

    if (result != TEEC_SUCCESS) {
        TMSLOG_E("%s failed, result = 0x%08X", __func__, result);
    }

    TEEC_ReleaseSharedMemory(&inputSM);
    return result;
}
