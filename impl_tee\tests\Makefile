# TMS SE impl_tee 单元测试 Makefile
# Copyright (c) Tsingteng MicroSystem Co., Ltd. 2024. All rights reserved.

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
RED    := \033[31m
GREEN  := \033[32m
YELLOW := \033[33m
BLUE   := \033[34m
RESET  := \033[0m

# 项目变量
PROJECT_NAME := tms_seimpl_tee_unit_tests
TEST_SCRIPT := ./run_tests.sh

# 检查Android环境
check-env:
	@if [ -z "$$ANDROID_BUILD_TOP" ]; then \
		echo "$(RED)错误: 未检测到Android构建环境$(RESET)"; \
		echo "请先执行: source build/envsetup.sh && lunch <target>"; \
		exit 1; \
	fi

# 显示帮助信息
help:
	@echo "$(BLUE)TMS SE impl_tee 单元测试构建系统$(RESET)"
	@echo ""
	@echo "$(YELLOW)可用目标:$(RESET)"
	@echo "  $(GREEN)help$(RESET)        - 显示此帮助信息"
	@echo "  $(GREEN)build$(RESET)       - 构建测试"
	@echo "  $(GREEN)test$(RESET)        - 运行所有测试"
	@echo "  $(GREEN)test-fast$(RESET)   - 构建并运行测试"
	@echo "  $(GREEN)clean$(RESET)       - 清理构建产物"
	@echo "  $(GREEN)install$(RESET)     - 安装测试到设备"
	@echo "  $(GREEN)coverage$(RESET)    - 生成代码覆盖率报告"
	@echo ""
	@echo "$(YELLOW)测试过滤选项:$(RESET)"
	@echo "  $(GREEN)test-adaptation$(RESET)  - 只运行适配层测试"
	@echo "  $(GREEN)test-gpapi$(RESET)       - 只运行GP API测试"
	@echo "  $(GREEN)test-config$(RESET)      - 只运行配置测试"
	@echo "  $(GREEN)test-platprop$(RESET)    - 只运行平台属性测试"
	@echo ""
	@echo "$(YELLOW)调试选项:$(RESET)"
	@echo "  $(GREEN)debug$(RESET)       - 详细模式构建和运行"
	@echo "  $(GREEN)xml$(RESET)         - 生成XML测试报告"
	@echo ""
	@echo "$(YELLOW)示例:$(RESET)"
	@echo "  make build          # 构建测试"
	@echo "  make test-fast      # 快速测试"
	@echo "  make test-adaptation # 只测试适配层"

# 构建测试
build: check-env
	@echo "$(BLUE)构建测试...$(RESET)"
	@$(TEST_SCRIPT) --build
	@echo "$(GREEN)构建完成$(RESET)"

# 运行所有测试
test: check-env
	@echo "$(BLUE)运行所有测试...$(RESET)"
	@$(TEST_SCRIPT) --run
	@echo "$(GREEN)测试完成$(RESET)"

# 快速测试（构建并运行）
test-fast: check-env
	@echo "$(BLUE)快速测试（构建并运行）...$(RESET)"
	@$(TEST_SCRIPT) --all
	@echo "$(GREEN)快速测试完成$(RESET)"

# 清理构建产物
clean: check-env
	@echo "$(BLUE)清理构建产物...$(RESET)"
	@$(TEST_SCRIPT) --clean
	@echo "$(GREEN)清理完成$(RESET)"

# 安装测试到设备
install: build
	@echo "$(BLUE)安装测试到设备...$(RESET)"
	@adb push $(ANDROID_PRODUCT_OUT)/vendor/bin/$(PROJECT_NAME) /vendor/bin/ || \
		(echo "$(RED)安装失败，请检查设备连接$(RESET)" && exit 1)
	@adb shell chmod 755 /vendor/bin/$(PROJECT_NAME)
	@echo "$(GREEN)安装完成$(RESET)"

# 生成代码覆盖率报告
coverage: check-env
	@echo "$(BLUE)生成代码覆盖率报告...$(RESET)"
	@$(TEST_SCRIPT) --coverage
	@echo "$(GREEN)覆盖率报告生成完成$(RESET)"

# 详细模式构建和运行
debug: check-env
	@echo "$(BLUE)详细模式构建和运行...$(RESET)"
	@$(TEST_SCRIPT) --all --verbose
	@echo "$(GREEN)调试运行完成$(RESET)"

# 生成XML测试报告
xml: check-env
	@echo "$(BLUE)生成XML测试报告...$(RESET)"
	@$(TEST_SCRIPT) --run --xml
	@echo "$(GREEN)XML报告生成完成$(RESET)"

# 特定模块测试
test-adaptation: check-env
	@echo "$(BLUE)运行适配层测试...$(RESET)"
	@$(TEST_SCRIPT) --run --filter "*Adaptation*"
	@echo "$(GREEN)适配层测试完成$(RESET)"

test-gpapi: check-env
	@echo "$(BLUE)运行GP API测试...$(RESET)"
	@$(TEST_SCRIPT) --run --filter "*GpApi*"
	@echo "$(GREEN)GP API测试完成$(RESET)"

test-config: check-env
	@echo "$(BLUE)运行配置测试...$(RESET)"
	@$(TEST_SCRIPT) --run --filter "*Config*"
	@echo "$(GREEN)配置测试完成$(RESET)"

test-platprop: check-env
	@echo "$(BLUE)运行平台属性测试...$(RESET)"
	@$(TEST_SCRIPT) --run --filter "*PlatProp*"
	@echo "$(GREEN)平台属性测试完成$(RESET)"

# 检查代码风格
lint:
	@echo "$(BLUE)检查代码风格...$(RESET)"
	@find . -name "*.cpp" -o -name "*.h" | xargs clang-format -i --style=file || \
		echo "$(YELLOW)警告: clang-format未找到，跳过代码格式化$(RESET)"
	@echo "$(GREEN)代码风格检查完成$(RESET)"

# 生成文档
docs:
	@echo "$(BLUE)生成文档...$(RESET)"
	@if command -v doxygen >/dev/null 2>&1; then \
		doxygen Doxyfile 2>/dev/null || echo "$(YELLOW)警告: Doxyfile未找到$(RESET)"; \
	else \
		echo "$(YELLOW)警告: doxygen未找到，跳过文档生成$(RESET)"; \
	fi
	@echo "$(GREEN)文档生成完成$(RESET)"

# 检查内存泄漏（需要valgrind支持）
memcheck: build
	@echo "$(BLUE)检查内存泄漏...$(RESET)"
	@echo "$(YELLOW)注意: 此功能需要在支持valgrind的环境中运行$(RESET)"
	@adb shell "valgrind --tool=memcheck --leak-check=full /vendor/bin/$(PROJECT_NAME)" || \
		echo "$(YELLOW)警告: valgrind不可用或测试失败$(RESET)"

# 性能测试
benchmark: build
	@echo "$(BLUE)运行性能测试...$(RESET)"
	@$(TEST_SCRIPT) --run --filter "*Benchmark*" || \
		echo "$(YELLOW)警告: 未找到性能测试用例$(RESET)"
	@echo "$(GREEN)性能测试完成$(RESET)"

# 持续集成目标
ci: clean build test xml
	@echo "$(GREEN)持续集成流程完成$(RESET)"

# 开发者快速检查
dev-check: lint build test-fast
	@echo "$(GREEN)开发者检查完成$(RESET)"

# 发布前检查
release-check: clean build test coverage docs
	@echo "$(GREEN)发布前检查完成$(RESET)"

# 显示构建信息
info:
	@echo "$(BLUE)构建信息:$(RESET)"
	@echo "  项目名称: $(PROJECT_NAME)"
	@echo "  Android构建顶级目录: $$ANDROID_BUILD_TOP"
	@echo "  目标产品: $$TARGET_PRODUCT"
	@echo "  构建变体: $$TARGET_BUILD_VARIANT"
	@echo "  输出目录: $$ANDROID_PRODUCT_OUT"

# 伪目标声明
.PHONY: help build test test-fast clean install coverage debug xml \
        test-adaptation test-gpapi test-config test-platprop \
        lint docs memcheck benchmark ci dev-check release-check info check-env
